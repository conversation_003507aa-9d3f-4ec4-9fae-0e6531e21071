
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LegalAssistant/client/src/components/TypewriterCTA.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">LegalAssistant/client/src/components</a> TypewriterCTA.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/69</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/69</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import { useEffect, useState } from "react";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import { motion } from "framer-motion";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const phrases = [</span>
<span class="cstat-no" title="statement not covered" >  "AiLex answers your calls",</span>
<span class="cstat-no" title="statement not covered" >  "AiLex answers your calls, organizes deadlines",</span>
<span class="cstat-no" title="statement not covered" >  "AiLex answers your calls, organizes deadlines, drafts documents",</span>
<span class="cstat-no" title="statement not covered" >  "AiLex answers your calls, organizes deadlines, drafts documents, and finds legal precedent",</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default function TypewriterCTA() {</span>
<span class="cstat-no" title="statement not covered" >  const [currentIndex, setCurrentIndex] = useState(0);</span>
<span class="cstat-no" title="statement not covered" >  const [displayText, setDisplayText] = useState("");</span>
<span class="cstat-no" title="statement not covered" >  const [isTyping, setIsTyping] = useState(true);</span>
<span class="cstat-no" title="statement not covered" >  const [currentLetterIndex, setCurrentLetterIndex] = useState(0);</span>
&nbsp;
  // Handle the typing animation for the current phrase
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!isTyping) return;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (currentLetterIndex &lt; phrases[currentIndex].length) {</span>
<span class="cstat-no" title="statement not covered" >      const typingTimer = setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        setDisplayText(</span>
<span class="cstat-no" title="statement not covered" >          phrases[currentIndex].substring(0, currentLetterIndex + 1)</span>
<span class="cstat-no" title="statement not covered" >        );</span>
<span class="cstat-no" title="statement not covered" >        setCurrentLetterIndex(currentLetterIndex + 1);</span>
<span class="cstat-no" title="statement not covered" >      }, 50); // Typing speed</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      return () =&gt; clearTimeout(typingTimer);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      setIsTyping(false);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [currentIndex, currentLetterIndex, isTyping]);</span>
&nbsp;
  // Handle progression to the next phrase
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (isTyping) return;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const progressTimer = setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (currentIndex &lt; phrases.length - 1) {</span>
<span class="cstat-no" title="statement not covered" >        setCurrentIndex(currentIndex + 1);</span>
<span class="cstat-no" title="statement not covered" >        setCurrentLetterIndex(0);</span>
<span class="cstat-no" title="statement not covered" >        setIsTyping(true);</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
        // Reset to first phrase after 3 seconds on the final phrase
<span class="cstat-no" title="statement not covered" >        setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          setCurrentIndex(0);</span>
<span class="cstat-no" title="statement not covered" >          setCurrentLetterIndex(0);</span>
<span class="cstat-no" title="statement not covered" >          setDisplayText("");</span>
<span class="cstat-no" title="statement not covered" >          setIsTyping(true);</span>
<span class="cstat-no" title="statement not covered" >        }, 3000);</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, 1500);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return () =&gt; clearTimeout(progressTimer);</span>
<span class="cstat-no" title="statement not covered" >  }, [currentIndex, isTyping]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="w-full bg-gradient-to-b from-lime-50 to-white py-16 px-4 relative overflow-hidden"&gt;</span>
      {/* Radial gradient background */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(184,255,92,0.2)_0%,_transparent_70%)] z-0"&gt;&lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      &lt;div className="max-w-5xl mx-auto text-center space-y-6 relative z-10"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-3xl sm:text-4xl font-bold text-gray-900 min-h-[100px] md:min-h-[80px] flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;span className="inline-block"&gt;{displayText}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;span className="inline-block w-[3px] h-8 bg-[#3B66F8] ml-1 animate-blink"&gt;&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/h2&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-600 text-lg max-w-xl mx-auto"&gt;</span>
          AiLex researches like a librarian, drafts like an associate, and
          organizes like a paralegal — all at solo-firm prices.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;motion.a</span>
<span class="cstat-no" title="statement not covered" >          href="/login"</span>
<span class="cstat-no" title="statement not covered" >          className="inline-block mt-4 px-6 py-3 bg-[#3B66F8] text-white text-lg font-medium rounded-lg shadow-md hover:bg-blue-700 transition-all"</span>
<span class="cstat-no" title="statement not covered" >          whileHover={{</span>
<span class="cstat-no" title="statement not covered" >            scale: 1.05,</span>
<span class="cstat-no" title="statement not covered" >            boxShadow: "0 10px 25px -5px rgba(59, 102, 248, 0.4)",</span>
<span class="cstat-no" title="statement not covered" >            transition: { duration: 0.2 },</span>
<span class="cstat-no" title="statement not covered" >          }}</span>
<span class="cstat-no" title="statement not covered" >          whileTap={{ scale: 0.98 }}</span>
<span class="cstat-no" title="statement not covered" >        &gt;</span>
          Try AiLex Free
<span class="cstat-no" title="statement not covered" >        &lt;/motion.a&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-28T12:17:18.155Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    
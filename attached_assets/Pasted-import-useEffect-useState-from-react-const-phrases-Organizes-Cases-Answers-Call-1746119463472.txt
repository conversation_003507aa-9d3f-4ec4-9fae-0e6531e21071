import { useEffect, useState } from 'react';

const phrases = [
  "Organizes Cases",
  "Answers Calls",
  "Drafts Legal Docs",
  "Researches the Law"
];

export default function CallToAction() {
  const [index, setIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [loopNum, setLoopNum] = useState(0);
  const [typingSpeed, setTypingSpeed] = useState(100);

  useEffect(() => {
    const handleType = () => {
      const fullText = phrases[loopNum % phrases.length];

      setDisplayText((prev) =>
        isDeleting ? fullText.substring(0, prev.length - 1) : fullText.substring(0, prev.length + 1)
      );

      setTypingSpeed(isDeleting ? 50 : 120);

      if (!isDeleting && displayText === fullText) {
        setTimeout(() => setIsDeleting(true), 1000);
      } else if (isDeleting && displayText === '') {
        setIsDeleting(false);
        setLoopNum(loopNum + 1);
      }
    };

    const timer = setTimeout(handleType, typingSpeed);
    return () => clearTimeout(timer);
  }, [displayText, isDeleting]);

  return (
    <section className="w-screen bg-gradient-to-br from-[#f6ffe6] to-white py-20">
      <div className="text-center px-4 max-w-3xl mx-auto">
        <div className="text-4xl font-bold text-gray-900 mb-4 h-14">
          <span className="relative">
            {displayText}
            <span className="animate-blink text-gray-900">|</span>
          </span>
        </div>
        <p className="text-lg text-gray-600 max-w-xl mx-auto mb-8">
          AiLex works like a full-time assistant—answering calls, organizing deadlines,
          drafting documents, and doing legal research so you don’t have to.
        </p>
        <button className="bg-[#3B66F8] text-white px-6 py-3 rounded-md hover:bg-blue-700 transition">
          Try AiLex Free
        </button>
      </div>
    </section>
  );
}

// Add this to your global.css or Tailwind layer:
<style>
{`
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}
.animate-blink {
  animation: blink 1s infinite;
}
`}
</style>

// Prompt to give Replit AI:
// "Create a React component for a call-to-action section. It replaces a static card and features a typewriter effect cycling through four AiLex benefits. Use AiLex's clean design, white background, center-aligned content, large headline, small subtext, and a blue CTA button."

// Code:
import React, { useEffect, useState } from 'react';

const phrases = [
  'AiLex answers your calls.',
  'AiLex organizes your deadlines.',
  'AiLex drafts your legal memos.',
  'So you can focus on winning cases.'
];

export default function TypewriterCTA() {
  const [currentText, setCurrentText] = useState('');
  const [index, setIndex] = useState(0);
  const [subIndex, setSubIndex] = useState(0);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (subIndex === phrases[index].length + 1 && !deleting) {
      setTimeout(() => setDeleting(true), 1500);
      return;
    }

    if (subIndex === 0 && deleting) {
      setDeleting(false);
      setIndex((prev) => (prev + 1) % phrases.length);
      return;
    }

    const timeout = setTimeout(() => {
      setSubIndex((prev) => prev + (deleting ? -1 : 1));
    }, deleting ? 30 : 70);

    return () => clearTimeout(timeout);
  }, [subIndex, index, deleting]);

  useEffect(() => {
    setCurrentText(phrases[index].substring(0, subIndex));
  }, [subIndex, index]);

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mt-12 text-center max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-900 h-12">
        <span>{currentText}</span>
        <span className="border-r-2 border-blue-500 animate-pulse ml-1"></span>
      </h2>
      <p className="mt-3 text-sm text-gray-600">
        AiLex works like a full-time assistant — answering calls, organizing deadlines, drafting documents, and more.
      </p>
      <button className="mt-5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-xl shadow">
        Try AiLex Free
      </button>
    </div>
  );
}
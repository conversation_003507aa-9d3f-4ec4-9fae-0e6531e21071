### 🖥️ AiLex Website—Detailed Visual Guide  
(one **root domain: ⁠ ailexlaw.com ⁠; state pages inherit this structure but swap hero copy/testimonials, e.g. ⁠ /tx ⁠, ⁠ /fl ⁠)  

---

#### 1. Global UX Frames

| Element | Spec |
|---------|------|
| *Grid* | 12-column, max-width *1280 px*; gutter 24 px; content constrained to 1040 px for readability. |
| *Breakpoints* | 0-599 px mobile · 600-1023 px tablet · 1024 px+ desktop. |
| *Palette* | Primary *#1EAEDB* (buttons, links) · Navy *#0C1C2D* (headers, footer bkg) · Cool gray *#F5F7FA* (section alt bkg) · Accent lime *#B8FF5C* for micro-highlights/CTAs. |
| *Typography* | Heading: Inter Extra-Bold 700 · Body: Inter Regular 400 · Code/labels: IBM Plex Mono 500. |
| *Corner radius* | 12 px on cards/buttons (soft, modern). |
| *Motion* | 250 ms ease-out for fades/slides; parallax flop for hero background (+10 px on scroll). |

---

#### 2. Component-by-Component Walk-Through  

 1.⁠ ⁠*Sticky Top-Nav (64 px high)*  
   * Left: AiLex logomark (stylised “A” with light-beam) → clicking always returns to root.  
   * Center: slim menu—Product | Pricing | Resources | Login.  
   * Right: *Primary CTA* button “Start free trial” (filled #1EAEDB, white text).  
   * Scroll state: background shifts from transparent to navy #0C1C2D @ 32 px scroll; logo swaps to light version.

 2.⁠ ⁠*Hero Section (Full viewport height)*  
   * *Split layout* (60 / 40):  
     * Left column:  
       * H1 (48 px): *“Your AI Legal Associate—Works While You’re in Court.”*  
       * Sub-headline (24 px): Research like a librarian, draft like an associate, organize like a paralegal—at solo-firm prices.  
       * Bullet strip with ✅ icons: State-specific research • Voice intake agent • SOC-2 ready.  
       * *Dual CTAs*:  
         * Primary: “Start 14-day free trial” (filled blue).  
         * Secondary: “Watch 90-sec demo” (outline, opens modal).  
     * Right column: looping *3-panel mockup carousel* (auto-switch every 6 s):  
       * Pane 1: PDF dropped → citations appear.  
       * Pane 2: voice call transcript auto-populated.  
       * Pane 3: Kanban board of active matters.  
   * Background: faint scale-animation of courthouse façade blueprint in 5 % white opacity.

 3.⁠ ⁠*Social Proof Strip (beneath fold, 100 px)*  
   * Logos of *Texas Bar CLE, **Maximum Lawyer Podcast, **Solo Practice University* (grayscale → color on hover).  

 4.⁠ ⁠*Problem → Solution Section* (alt gray bkg)  
   * Three horizontal cards (stacked on mobile):  
     1. “⏳ 40 % lawyer time is unbilled admin.”  
     2. “😰 Missed intake calls cost solos $7 k+/yr.”  
     3. “💸 Big-law software starts at $199/user.”  
   * Underneath: animated arrow slides to *single large card* “AiLex fixes all three—here’s how”.

 5.⁠ ⁠*Feature Trio (dark navy bkg, light text)*  
   * *Research Module* card—click ↗️ flips to show inline video 15 s (code snippet w/ citations).  
   * *Drafting Co-Pilot* card—shows Word-style doc with suggestion highlights.  
   * *Voice Intake Agent* card—SVG waveform + transcript streaming effect.  
   * Each card has small pill “TX-Ready” automatically replaced by “FL-Ready” etc. via dynamic content.

 6.⁠ ⁠*Interactive Calculator* (white bkg)  
   * Slider: How many hours/week do you spend on research + drafting? (0-20).  
   * Result auto-animates savings chart and ROI number “Save \$X per month” (JS).  
   * CTA “See full savings for your firm” → moves user to pricing table.

 7.⁠ ⁠*Testimonials & Case Studies* (gray bkg)  
   * Two-column masonry layout; video bubbles 1:1 aspect ratio + text quotes.  
   * Tag chips (blue) to switch state: TX, FL, NY (filters testimonials instantly).

 8.⁠ ⁠*Pricing Section* (white)  
   * 3 cards, subtle shadow hover-lift.  
   * Toggle “Monthly / Annual (-15 %)”.  
   * Each card footer has microcopy “No hidden fees. Cancel anytime.”  
   * FAQ accordion directly below for friction reduction.

 9.⁠ ⁠*Security & Compliance Banner* (navy bkg, 50 vh)  
   * SOC-2 icon, GDPR shield, encryption badge; link to detailed PDF.

10.⁠ ⁠*Footer*  
    * Left: logo + mini-mission line “Built for small firms, powered by big tech.”  
    * Center: quick links columns.  
    * Right: signup form (email + “Get weekly AI tip”).  
    * Bottom-bar: ©️, privacy, terms; tiny “Made with ❤️ in Kigali & Austin”.

---

#### 3. Micro-Interactions & Delight

•⁠  ⁠*Scroll indicator* under hero CTA gently pulses every 3 s.  
•⁠  ⁠*State switcher* top-right on ⁠ /tx ⁠, ⁠ /fl ⁠, etc.—select menu animates flag icon.  
•⁠  ⁠*Cookie notice* slides in from bottom; dark-mode auto-detected based on OS.  
•⁠  ⁠*Form fields* use password-strength-style “ethics-check” bar: turns green when user accepts “Verify citations?”.

---

#### 4. Mobile Priorities

•⁠  ⁠Hero carousel becomes swipeable; CTAs pinned bottom as slide-up sheet.  
•⁠  ⁠Pricing cards collapse into accordion with sticky plan-toggle.  
•⁠  ⁠Phone icon fixed bottom-right opens Click-to-call VoIP demo.

---

#### 5. Performance & Tech Stack

| Layer | Choice | Reason |
|-------|--------|--------|
| *Frontend* | Next.js (App Router) + Tailwind | Instant route prefetch, easy per-state pages. |
| *CMS* | Headless (Prismic / Sanity) | Marketing can edit copy without dev. |
| *Hosting* | Vercel Edge | Blazing TTFB + automatic image optimization. |
| *Analytics* | Plausible (self-host) | GDPR-friendly. |
| *A/B tests* | Vercel Experiments | Flip hero headline variants fast. |

---

### Visual Cheat-Sheet (quick mental picture)

┌──────────────────────────────────────────────┐
│ Nav | Logo  | Product | Pricing | Login | ☰ │
├──────────────────────────────────────────────┤
│  HERO  ───── “Your AI Legal Associate …”     │
│  [ Start Trial ] [ Watch Demo ]              │
│  ┌─────── Mockup Carousel ───────────────┐   │
├──────────────────────────────────────────────┤
│  Trusted by badges                          │
├──────────────────────────────────────────────┤
│  Problem cards → Solution card              │
├──────────────────────────────────────────────┤
│  Feature : Research | Draft | Intake        │
│   (dark section with video hovers)          │
├──────────────────────────────────────────────┤
│  ROI Slider  |  CTA                         │
├──────────────────────────────────────────────┤
│  Testimonials (filter chips)                │
├──────────────────────────────────────────────┤
│  Pricing cards + FAQ                        │
├──────────────────────────────────────────────┤
│  Security banner                            │
├──────────────────────────────────────────────┤
│  Footer (signup, links)                     │
└──────────────────────────────────────────────┘


---

### What’s Next?
 1.⁠ ⁠*Wireframe* in Figma using the grid & palette above.  
 2.⁠ ⁠Approve copy for hero + feature cards.  
 3.⁠ ⁠Export SVG icons & micro-animations (Lottie).  

Tell me when you’re ready for a clickable wireframe outline or component-level Tailwind code—happy to supply the next chunk.
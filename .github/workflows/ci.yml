name: CI

on:
  pull_request:
    branches: [develop]

jobs:
  verify:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"
      - run: pnpm install --frozen-lockfile
      - run: pnpm run lint
      - run: pnpm run typecheck
      - run: pnpm run test:ci
      - run: pnpm run build
      - name: Bundle budget
        run: |
          SIZE=$(stat -c%s dist/public/assets/index-*.js)
          [ "$SIZE" -le 900000 ] || { echo "Bundle too big: $SIZE bytes"; exit 1; }

  audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"
      - run: pnpm install --frozen-lockfile
      - run: pnpm audit --audit-level=moderate

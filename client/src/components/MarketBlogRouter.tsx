import { useRoute } from "wouter";
import Blog from "@/pages/Blog";
import BlogPost from "@/pages/BlogPost";

export default function MarketBlogRouter() {
  // Simple route matching - no complex market detection
  const [matchBlogRoot] = useRoute("/blog");
  const [matchBlogPost, paramsBlogPost] = useRoute("/blog/:slug");

  // Simple render logic
  if (matchBlogPost && paramsBlogPost?.slug) {
    return (
      <BlogPost market="global" language="en" slug={paramsBlogPost.slug} />
    );
  }

  if (matchBlogRoot) {
    return <Blog market="global" language="en" />;
  }

  // Fallback
  return <Blog market="global" language="en" />;
}

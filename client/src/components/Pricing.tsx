import { useState } from "react";
import { motion } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  User,
  Briefcase,
  FileText,
  Inbox,
  Calendar,
  CheckCircle,
  PlusCircle,
  XCircle,
  ShieldCheck,
  Building,
  Code,
  Phone,
  Bot,
  MessageSquareShare,
} from "lucide-react";

export default function Pricing() {
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "annual">(
    "monthly"
  );
  const [showCompareFeatures, setShowCompareFeatures] = useState(false);

  // Pricing calculation
  const getPrice = (basePrice: number): number => {
    if (billingPeriod === "annual") {
      return Math.round(basePrice * 0.85); // 15% discount for annual
    }
    return basePrice;
  };

  const pricingPlans = [
    {
      name: "Solo",
      subtitle: "LAWYER & ASSISTANT",
      price: getPrice(99),
      features: [
        "Lawyer + 1 Free Assistant seat",
        "Choose 1 legal practice: PI, Family, or Criminal",
        "50 research queries & 20 document drafts/month",
        "Client intake (Email + form)",
        "Task & deadline management",
        "Case management & Document upload",
        "➕ Add AI Receptionist for intake & scheduling (100 min/month) for $29/month",
        "➕ Add extra user for $29/month",
      ],
      disabledFeatures: [],
      popular: false,
      cta: "Start free trial",
    },
    {
      name: "Team",
      subtitle: "SMALL FIRM",
      price: getPrice(199),
      features: [
        "<span class='font-medium'>5 users included</span>",
        "<span class='font-medium'>✅ Everything in SOLO, plus:</span>",
        "<span class='font-medium'>All legal practices included</span> (PI, Family, Criminal)",
        "<span class='font-medium'>Unlimited research & drafts*</span>",
        "AI Receptionist for intake & scheduling (<span class='font-medium'>250 mins/month</span>)",
        "➕ Add extra user for $29/month",
      ],
      disabledFeatures: [],
      popular: true,
      cta: "Start free trial",
    },
    {
      name: "Scale",
      subtitle: "GROWING BUSINESSES",
      price: getPrice(299),
      features: [
        "<span class='font-medium'>10 users included</span>",
        "<span class='font-medium'>✅ Everything in TEAM, plus:</span>",
        "AI Receptionist (<span class='font-medium'>500 mins/month</span>)",
        "Custom API integration",
        "Dedicated onboarding & support",
        "Priority access to new jurisdictions",
        "➕ Add extra user for $29/month",
      ],
      disabledFeatures: [],
      popular: false,
      cta: "Contact sales",
    },
  ];

  const faqs = [
    {
      question: "How does AiLex keep my data secure?",
      answer:
        "AiLex encrypts all data in transit using TLS 1.3 and at rest with AES-256, using per-tenant keys managed by hardware security modules (Google KMS). We're built on SOC-2 certified infrastructure, and full SOC-2 compliance is on our roadmap. Even our system admins can't access your privileged documents. Your data stays protected — from intake to insights.",
    },
    {
      question: "Do you train your AI on client data?",
      answer:
        "Never. AiLex does not use your client data to train our AI — not now, not ever. Your data is always yours, and you can export or delete it at any time.",
    },
    {
      question: "Do I need to change my current systems?",
      answer:
        "No. AiLex works alongside your existing tools. We offer integrations with popular legal software like Clio, MyCase, and LawPay, but you can also use AiLex as a standalone solution.",
    },
    {
      question: "How quickly can I get started?",
      answer:
        "Immediately. No training. No onboarding.\nAiLex is designed to work for you straight out of the box — zero setup needed.\nAnd if you do want help, our team is just one email away.",
    },
  ];

  return (
    <section
      id="pricing"
      className="relative py-24 overflow-hidden bg-gradient-to-b from-[#edf2fa] via-[#f5f9ff] to-white"
    >
      {/* Enhanced cloud-like background with more visible elements */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Main cloud elements with increased opacity and size */}
        <div className="absolute -top-20 -left-20 w-[600px] h-[500px] rounded-full bg-blue-100/50 filter blur-[80px]"></div>
        <div className="absolute top-40 right-0 w-[700px] h-[500px] rounded-full bg-blue-50/60 filter blur-[100px]"></div>
        <div className="absolute bottom-40 left-10 w-[600px] h-[400px] rounded-full bg-blue-100/45 filter blur-[90px]"></div>
        <div className="absolute -bottom-20 right-0 w-[550px] h-[500px] rounded-full bg-blue-50/55 filter blur-[100px]"></div>

        {/* Center atmospheric glow */}
        <div className="absolute top-[30%] left-[35%] w-[800px] h-[500px] rounded-full bg-gradient-to-br from-blue-50/40 to-white/20 filter blur-[120px]"></div>

        {/* Subtle white highlights for depth */}
        <div className="absolute top-80 left-[30%] w-[300px] h-[200px] rounded-full bg-white/50 filter blur-[70px]"></div>
        <div className="absolute top-20 right-[20%] w-[250px] h-[250px] rounded-full bg-white/60 filter blur-[80px]"></div>

        {/* Enhanced lime green accents */}
        <div className="absolute top-[25%] right-[15%] w-[200px] h-[200px] rounded-full bg-[#B8FF5C]/30 filter blur-[70px]"></div>
        <div className="absolute bottom-[30%] left-[20%] w-[250px] h-[250px] rounded-full bg-[#B8FF5C]/25 filter blur-[80px]"></div>
        <div className="absolute top-[60%] right-[30%] w-[180px] h-[180px] rounded-full bg-[#B8FF5C]/35 filter blur-[60px]"></div>
        <div className="absolute bottom-[70%] left-[25%] w-[150px] h-[150px] rounded-full bg-[#B8FF5C]/30 filter blur-[50px]"></div>
      </div>

      {/* Subtle radial overlay for atmosphere */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_rgba(240,248,255,0.3),_transparent_70%)] z-0"></div>

      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-b from-blue-50 to-transparent rounded-full transform translate-x-1/3 -translate-y-1/3 opacity-70"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-t from-indigo-50 to-transparent rounded-full transform -translate-x-1/4 translate-y-1/4 opacity-70"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-12 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">Pricing</span>
          </div>
          <h2 className="text-3xl font-semibold mb-4 text-center text-gray-800">
            Simple, Transparent Pricing
          </h2>
          <p className="text-gray-600 text-center mb-8 max-w-2xl mx-auto">
            Choose the plan that fits your practice needs. All plans include our
            core AI features.
          </p>
        </motion.div>

        <motion.div
          className="flex justify-center mb-8 mt-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-[#F5F7FA] rounded-full p-1 inline-flex">
            <button
              onClick={() => setBillingPeriod("monthly")}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-default ${
                billingPeriod === "monthly"
                  ? "bg-white text-navy"
                  : "text-gray-600 hover:bg-white hover:text-navy"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingPeriod("annual")}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-default ${
                billingPeriod === "annual"
                  ? "bg-white text-navy"
                  : "text-gray-600 hover:bg-white hover:text-navy"
              }`}
            >
              Annual (-15%)
            </button>
          </div>
        </motion.div>

        <div className="flex flex-wrap justify-center gap-4 md:flex-nowrap mx-auto">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={plan.name}
              className={`
                w-[460px] min-h-[540px] px-8 py-10 rounded-2xl transition-all duration-300 
                relative cursor-pointer backdrop-blur-sm border border-blue-100/30
                ${
                  plan.popular
                    ? "bg-white/95 shadow-[0_20px_50px_rgba(8,112,184,0.15)] scale-[1.02] ring-2 ring-blue-200/60"
                    : "bg-blue-50/80 shadow-[0_10px_30px_rgba(0,0,0,0.08)]"
                }
                ${
                  plan.popular
                    ? "hover:shadow-[0_30px_60px_rgba(8,112,184,0.2)] hover:translate-y-[-15px] hover:scale-[1.01] hover:bg-white"
                    : "hover:shadow-[0_20px_40px_rgba(0,0,0,0.12)] hover:translate-y-[-12px] hover:scale-[1.005] hover:ring-1 hover:ring-blue-100 hover:bg-blue-50/90"
                }
              `}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
            >
              {plan.popular && (
                <div className="absolute -top-3 right-6 bg-gradient-to-r from-[#0C1C2D] to-[#1a3c5d] text-white text-xs font-medium py-1.5 px-4 rounded-full shadow-lg transform scale-110 border border-blue-300/20 backdrop-blur-sm">
                  <div className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3.5 w-3.5 mr-1.5 text-[#B8FF5C]"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Most Popular
                  </div>
                </div>
              )}

              <div className="flex flex-col h-full">
                {/* Header section */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {plan.name.charAt(0).toUpperCase() +
                      plan.name.slice(1).toLowerCase()}
                  </h3>
                  <div className="mb-6">
                    <div className="text-5xl font-bold text-gray-900">
                      ${plan.price}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {plan.name === "Solo" &&
                        `Includes 2 users (~$${billingPeriod === "monthly" ? "50" : "42"}/user)`}
                      {plan.name === "Team" &&
                        `Includes 5 users (~$${billingPeriod === "monthly" ? "40" : "34"}/user)`}
                      {plan.name === "Scale" &&
                        `Includes 10 users (~$${billingPeriod === "monthly" ? "30" : "25"}/user)`}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      per month, billed{" "}
                      {billingPeriod === "monthly" ? "monthly" : "annually"}
                    </div>
                  </div>
                </div>

                {/* Button section */}
                <div className="mb-6">
                  <a
                    href="/login"
                    className={`block w-full rounded-full px-6 py-3 font-medium transition-all duration-300 hover:scale-[1.01] transition-transform ease-in-out text-center ${
                      plan.name === "Solo"
                        ? "bg-[#1D8DF1] text-white hover:bg-[#1479c9] hover:shadow-md"
                        : plan.name === "Team"
                          ? "bg-black text-white hover:bg-gray-800 hover:shadow-md"
                          : "bg-[#E5F3FF] text-[#1D8DF1] hover:bg-[#D7ECFF] hover:shadow-md"
                    }`}
                  >
                    {plan.cta}
                  </a>
                </div>

                <hr className="border-t border-dashed border-gray-200 my-2" />

                {/* Plan heading */}
                <p className="text-sm font-medium text-gray-500 mb-5">
                  {plan.name === "Solo"
                    ? "You will get:"
                    : plan.name === "Team"
                      ? "Everything in SOLO plus:"
                      : "Everything in TEAM plus:"}
                </p>

                {/* Features section */}
                <div className="flex-grow">
                  <div className="space-y-1 mt-1">
                    {plan.features.map((feature, idx) => {
                      // Determine which icon to use based on content
                      let FeatureIcon = CheckCircle;
                      let stripPrefix = false;

                      if (idx === 0) {
                        // User licenses feature
                        FeatureIcon = User;
                      } else if (idx === 1) {
                        // Legal practices or 'Everything in X' feature
                        FeatureIcon = feature.includes("Everything")
                          ? CheckCircle
                          : Briefcase;
                      } else if (
                        feature.toLowerCase().includes("research") ||
                        feature.toLowerCase().includes("draft")
                      ) {
                        // Research and drafts related feature
                        FeatureIcon = FileText;
                      } else if (feature.toLowerCase().includes("intake")) {
                        // Intake related feature
                        FeatureIcon = Inbox;
                      } else if (
                        feature.toLowerCase().includes("task") ||
                        feature.toLowerCase().includes("deadline")
                      ) {
                        // Task management feature
                        FeatureIcon = Calendar;
                      } else if (
                        feature.toLowerCase().includes("case") ||
                        feature.toLowerCase().includes("document")
                      ) {
                        // Case management feature
                        FeatureIcon = FileText;
                      } else if (
                        feature.toLowerCase().includes("ai receptionist")
                      ) {
                        // AI Receptionist feature with modern Framer style
                        FeatureIcon = Bot;
                      } else if (
                        feature.toLowerCase().includes("multi-state")
                      ) {
                        // Multi-state compliance
                        FeatureIcon = Building;
                      } else if (feature.toLowerCase().includes("dedicated")) {
                        // Dedicated support
                        FeatureIcon = ShieldCheck;
                      } else if (feature.toLowerCase().includes("api")) {
                        // API integration
                        FeatureIcon = Code;
                      } else if (feature.toLowerCase().includes("priority")) {
                        // Priority access
                        FeatureIcon = ShieldCheck;
                      }

                      // Check if it's an add-on (previously prefixed with ➕)
                      if (feature.startsWith("➕")) {
                        FeatureIcon = PlusCircle;
                        stripPrefix = true;
                      }

                      // Clean up the feature text by removing emoji prefixes
                      let cleanFeature = feature;
                      if (stripPrefix) {
                        cleanFeature = feature.substring(
                          feature.indexOf(" ") + 1
                        );
                      }

                      // For Team and Scale plans, show the user count as the first feature
                      if (plan.name === "Team" && idx === 0) {
                        return (
                          <div
                            key={idx}
                            className="flex items-start gap-3 mb-2"
                          >
                            <CheckCircle className="w-5 h-5 shrink-0 text-[#0C1C2D] mt-0.5" />
                            <div className="text-sm text-gray-700 leading-relaxed">
                              5 users included
                            </div>
                          </div>
                        );
                      }

                      if (plan.name === "Scale" && idx === 0) {
                        return (
                          <div
                            key={idx}
                            className="flex items-start gap-3 mb-2"
                          >
                            <CheckCircle className="w-5 h-5 shrink-0 text-[#0C1C2D] mt-0.5" />
                            <div className="text-sm text-gray-700 leading-relaxed">
                              10 users included
                            </div>
                          </div>
                        );
                      }

                      // Skip other redundant features for Team and Scale
                      if (
                        (plan.popular || plan.name === "Scale") &&
                        (idx === 0 ||
                          idx === 1 ||
                          feature.includes("Everything"))
                      ) {
                        return null;
                      }

                      // Determine if this feature is an add-on that should use the plus icon
                      const isAddOn =
                        feature.toLowerCase().includes("add ai receptionist") ||
                        feature.toLowerCase().includes("add extra user");

                      return (
                        <div key={idx} className="flex items-start gap-3 mb-3">
                          {isAddOn ? (
                            <PlusCircle className="w-5 h-5 shrink-0 text-[#1EAEDB] mt-0.5" />
                          ) : (
                            <CheckCircle className="w-5 h-5 shrink-0 text-[#0C1C2D] mt-0.5" />
                          )}
                          <div
                            className="text-sm text-gray-700 leading-relaxed"
                            dangerouslySetInnerHTML={{
                              __html: stripPrefix ? cleanFeature : feature,
                            }}
                          ></div>
                        </div>
                      );
                    })}

                    {plan.disabledFeatures.map((feature, idx) => (
                      <div
                        key={idx}
                        className="flex items-start gap-3 text-gray-400 mb-3"
                      >
                        <XCircle className="w-5 h-5 shrink-0 text-gray-300 mt-0.5" />
                        <div className="text-sm text-gray-400 leading-relaxed">
                          {feature}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Footer section with Fair Use disclaimer for Team plan */}
                <div className="mt-4">
                  {plan.name === "Team" && (
                    <div className="text-gray-500 text-xs text-center pt-2 border-t border-gray-100">
                      * Fair Use applies
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-8 w-full flex justify-center">
          <div className="bg-gray-100 text-gray-600 text-sm px-5 py-2 rounded-full shadow-sm">
            No hidden fees. Cancel anytime.
          </div>
        </div>

        {/* Compare Features Section */}
        <motion.div
          className="mt-20 max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {/* Collapsible Section with useState */}
          <div className="text-center">
            <button
              onClick={() => setShowCompareFeatures(!showCompareFeatures)}
              className="inline-flex items-center gap-2 text-gray-700 hover:text-gray-900 bg-white px-5 py-3 rounded-full shadow-sm border border-gray-200 transition-all duration-200 hover:shadow"
            >
              <h3 className="text-base font-medium">Compare Features</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 w-5 transition-transform duration-200 ${showCompareFeatures ? "rotate-180" : "rotate-0"}`}
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <p className="text-gray-600 max-w-2xl mx-auto mt-2">
              See which plan is right for your practice
            </p>
          </div>

          {/* Animated comparison table that only shows when expanded */}
          {showCompareFeatures && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="overflow-x-auto rounded-2xl shadow-sm border border-gray-200 bg-white max-w-6xl mx-auto mt-8">
                <div className="overflow-x-auto relative">
                  {/* Vertical highlight for Team column */}
                  <div
                    className="absolute top-0 bottom-0 bg-blue-50 z-0"
                    style={{ left: "calc(50% - 80px)", width: "160px" }}
                  ></div>
                  <table className="table-fixed w-full text-sm text-left text-gray-700 min-w-[700px] relative z-10">
                    <thead>
                      <tr className="border-b border-gray-200 bg-gray-50">
                        <th className="py-5 px-6 text-left font-medium text-gray-500">
                          Features
                        </th>
                        <th className="py-5 px-6 text-center font-medium text-[#1D8DF1]">
                          Solo
                        </th>
                        <th className="py-5 px-6 text-center font-medium text-black relative bg-blue-50 ring-1 ring-blue-100">
                          <div className="relative pt-3 mt-3">
                            Team
                            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6">
                              <span className="inline-flex items-center text-[10px] font-medium bg-black text-white py-1 px-3 rounded-full shadow-md border border-gray-700 z-20 whitespace-nowrap">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-2.5 w-2.5 mr-1 text-[#B8FF5C]"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                >
                                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                                </svg>
                                Popular
                              </span>
                            </div>
                          </div>
                        </th>
                        <th className="py-5 px-6 text-center font-medium text-[#1D8DF1]">
                          Scale
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-gray-100 bg-white">
                        <td className="py-4 px-6 text-gray-700">User seats</td>
                        <td className="py-4 px-6 text-center">
                          1 + 1 Assistant
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          5 users
                        </td>
                        <td className="py-4 px-6 text-center">10 users</td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          Practice areas
                        </td>
                        <td className="py-4 px-6 text-center">Choose 1</td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          All included
                        </td>
                        <td className="py-4 px-6 text-center">All included</td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-white">
                        <td className="py-4 px-6 text-gray-700">AI Research</td>
                        <td className="py-4 px-6 text-center">
                          50 queries/month
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          Unlimited
                        </td>
                        <td className="py-4 px-6 text-center">Unlimited</td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          Document drafting
                        </td>
                        <td className="py-4 px-6 text-center">
                          20 drafts/month
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          Unlimited
                        </td>
                        <td className="py-4 px-6 text-center">Unlimited</td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-white">
                        <td className="py-4 px-6 text-gray-700">
                          Client intake (Email + form)
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-black"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          Task management
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-black"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-white">
                        <td className="py-4 px-6 text-gray-700">
                          Case management
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-black"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          Deadline reminders & Calendar
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-black"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-white">
                        <td className="py-4 px-6 text-gray-700">
                          Document uploads
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-black"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          AI Receptionist
                        </td>
                        <td className="py-4 px-6 text-center">
                          <span className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs font-medium">
                            $29/month add-on
                          </span>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          250 mins/month
                        </td>
                        <td className="py-4 px-6 text-center">
                          500 mins/month
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          Custom API integration
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-100 bg-white">
                        <td className="py-4 px-6 text-gray-700">
                          Dedicated support
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-black"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="py-4 px-6 text-gray-700">
                          Priority access to new features
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50">
                          <svg
                            className="h-5 w-5 mx-auto text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </td>
                        <td className="py-4 px-6 text-center">
                          <svg
                            className="h-5 w-5 mx-auto text-[#1D8DF1]"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Get In Touch Section */}
        <motion.div
          className="mt-24 max-w-4xl mx-auto"
          id="contact"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="bg-gradient-to-r from-[#0C1C2D] to-[#1a3c5d] rounded-2xl overflow-hidden shadow-xl">
            <div className="flex flex-col md:flex-row items-center">
              <div className="p-10 md:p-12 md:w-1/2">
                <h2 className="text-2xl font-semibold text-white mb-4">
                  Get In Touch
                </h2>
                <p className="text-blue-100 mb-6">
                  Our team is ready to help you find the right solution for your
                  practice.
                </p>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-[#B8FF5C] mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Schedule a call</p>
                      <p className="text-blue-200 text-sm">
                        Get personalized advice from our team
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <MessageSquareShare className="h-5 w-5 text-[#B8FF5C] mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Live demo</p>
                      <p className="text-blue-200 text-sm">
                        See AiLex in action with real cases
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <FileText className="h-5 w-5 text-[#B8FF5C] mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Custom quote</p>
                      <p className="text-blue-200 text-sm">
                        For larger firms with specific needs
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-900/40 backdrop-blur-sm p-10 md:p-12 md:w-1/2 border-t md:border-t-0 md:border-l border-blue-800/50">
                <form className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-100 mb-1">
                        First Name
                      </label>
                      <input
                        type="text"
                        className="w-full px-4 py-2 bg-white/10 border border-blue-800/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#B8FF5C]/50 text-white"
                        placeholder="First name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-100 mb-1">
                        Last Name
                      </label>
                      <input
                        type="text"
                        className="w-full px-4 py-2 bg-white/10 border border-blue-800/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#B8FF5C]/50 text-white"
                        placeholder="Last name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-100 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      className="w-full px-4 py-2 bg-white/10 border border-blue-800/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#B8FF5C]/50 text-white"
                      placeholder="Your email address"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-100 mb-1">
                      Message
                    </label>
                    <textarea
                      className="w-full px-4 py-2 bg-white/10 border border-blue-800/50 rounded-lg h-24 focus:outline-none focus:ring-2 focus:ring-[#B8FF5C]/50 text-white resize-none"
                      placeholder="How can we help?"
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-[#B8FF5C] text-[#0C1C2D] hover:bg-[#a5eb49] px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:shadow-lg"
                  >
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          className="mt-16 max-w-3xl mx-auto"
          id="faq"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <div className="bg-white rounded-2xl shadow-md px-6 py-10">
            <div className="text-xs uppercase text-gray-400 tracking-wide mb-2 text-center">
              Your Questions
            </div>
            <h2 className="text-xl font-semibold mb-6 text-center">FAQs</h2>

            <div className="space-y-4">
              <Accordion type="single" collapsible className="w-full">
                {faqs.map((faq, index) => (
                  <AccordionItem
                    key={index}
                    value={`faq-${index}`}
                    className="border border-gray-200 rounded-xl overflow-hidden mb-4 transition-all duration-200 ease-in-out"
                  >
                    <AccordionTrigger className="hover:bg-[#F5F7FA] px-4 py-4 font-medium transition-all duration-200 ease-in-out">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="px-4 py-4 border-t border-gray-200 bg-[#F5F7FA] transition-all duration-200 ease-in-out">
                      <p className="text-gray-600">{faq.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

import React, { useState, useCallback, useRef } from "react";
import {
  Upload,
  X,
  Image as ImageIcon,
  Loader2,
  Check,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";

interface ImageUploadProps {
  value?: string;
  onChange: (url: string) => void;
  onRemove?: () => void;
  className?: string;
}

interface UploadedImage {
  id: string;
  url: string;
  name: string;
  size: number;
  uploadedAt: Date;
}

export default function ImageUpload({
  value,
  onChange,
  onRemove,
  className,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [recentUploads, setRecentUploads] = useState<UploadedImage[]>([]);
  const [showGallery, setShowGallery] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Load recent uploads on component mount
  React.useEffect(() => {
    loadRecentUploads();
  }, []);

  const loadRecentUploads = async () => {
    try {
      const { data, error } = await supabase.storage
        .from("blog-images")
        .list("", {
          limit: 20,
          offset: 0,
          sortBy: { column: "created_at", order: "desc" },
        });

      if (error) throw error;

      const uploads: UploadedImage[] = data.map((file) => ({
        id: file.id || file.name,
        url: supabase.storage.from("blog-images").getPublicUrl(file.name).data
          .publicUrl,
        name: file.name,
        size: file.metadata?.size || 0,
        uploadedAt: new Date(file.created_at || Date.now()),
      }));

      setRecentUploads(uploads);
    } catch (error) {
      console.error("Error loading recent uploads:", error);
    }
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"];
    if (!allowedTypes.includes(file.type)) {
      return "Please upload a valid image file (JPG, PNG, WebP, or GIF)";
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return "File size must be less than 5MB";
    }

    return null;
  };

  const uploadFile = async (file: File) => {
    const validation = validateFile(file);
    if (validation) {
      toast({
        title: "Upload Error",
        description: validation,
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Generate unique filename
      const fileExt = file.name.split(".").pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      // Upload to Supabase Storage
      const { error } = await supabase.storage
        .from("blog-images")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) throw error;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from("blog-images")
        .getPublicUrl(fileName);

      const imageUrl = urlData.publicUrl;

      // Update form
      onChange(imageUrl);

      // Add to recent uploads
      const newUpload: UploadedImage = {
        id: fileName,
        url: imageUrl,
        name: file.name,
        size: file.size,
        uploadedAt: new Date(),
      };
      setRecentUploads((prev) => [newUpload, ...prev.slice(0, 19)]);

      toast({
        title: "Upload Successful",
        description: "Image uploaded and ready to use!",
      });
    } catch (error: unknown) {
      console.error("Upload error:", error);
      toast({
        title: "Upload Failed",
        description:
          error instanceof Error ? error.message : "Failed to upload image",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      uploadFile(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      uploadFile(e.target.files[0]);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={className}>
      <Label className="text-sm font-medium mb-3 block">Featured Image</Label>

      {/* Current Image Display */}
      {value && (
        <div className="mb-4">
          <div className="relative inline-block">
            <img
              src={value}
              alt="Current featured image"
              className="max-w-xs h-auto rounded-lg border shadow-sm"
              onError={(e) => {
                e.currentTarget.style.display = "none";
              }}
            />
            {onRemove && (
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                onClick={() => {
                  onRemove();
                  onChange("");
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Upload Methods */}
      <div className="space-y-4">
        {/* Drag & Drop Upload */}
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
            ${dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"}
            ${isUploading ? "pointer-events-none opacity-50" : "cursor-pointer"}
          `}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          {isUploading ? (
            <div className="space-y-2">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              <p className="text-sm text-gray-600">Uploading image...</p>
              {uploadProgress > 0 && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="h-8 w-8 mx-auto text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  JPG, PNG, WebP or GIF (max 5MB)
                </p>
              </div>
            </div>
          )}
        </div>

        {/* URL Input Alternative */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">Or paste image URL:</Label>
          <Input
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder="https://example.com/image.jpg"
            className="text-sm"
          />
        </div>

        {/* Image Gallery Toggle */}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowGallery(!showGallery)}
          className="w-full"
        >
          <ImageIcon className="h-4 w-4 mr-2" />
          {showGallery ? "Hide" : "Show"} Recent Uploads ({recentUploads.length}
          )
        </Button>
      </div>

      {/* Recent Uploads Gallery */}
      {showGallery && recentUploads.length > 0 && (
        <div className="mt-4 p-4 border rounded-lg bg-gray-50">
          <h4 className="text-sm font-medium mb-3">Recent Uploads</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            {recentUploads.map((upload) => (
              <div
                key={upload.id}
                className="relative group cursor-pointer"
                onClick={() => onChange(upload.url)}
              >
                <img
                  src={upload.url}
                  alt={upload.name}
                  className="w-full aspect-square object-cover rounded border hover:border-blue-500 transition-colors"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity rounded flex items-center justify-center">
                  <Check className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity">
                  <p className="truncate">{upload.name}</p>
                  <p>{formatFileSize(upload.size)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Image Guidelines */}
      <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3">
        <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          Image Guidelines
        </h4>
        <div className="text-xs text-blue-800 space-y-1">
          <p>
            <strong>Recommended:</strong> 1200×630px (16:9 ratio) for optimal
            social sharing
          </p>
          <p>
            <strong>Formats:</strong> JPG, PNG, WebP, or GIF
          </p>
          <p>
            <strong>Size limit:</strong> 5MB maximum
          </p>
          <p>
            <strong>Quality:</strong> High-resolution, professional images work
            best
          </p>
        </div>
      </div>
    </div>
  );
}

.hero-background-alt {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.blob {
  position: absolute;
  border-radius: 50%;
  filter: blur(30px);
  will-change: transform;
  opacity: 0.25; /* Increased opacity */
  box-shadow: 0 0 60px rgba(200, 220, 255, 0.2); /* Add glow effect */
}

.blob-1 {
  width: 650px;
  height: 650px;
  left: -10%;
  top: -20%;
  background: radial-gradient(
    circle at center,
    rgba(230, 245, 252, 0.9),
    rgba(245, 247, 250, 0.6)
  );
  animation: blob-float-1 40s ease-in-out infinite alternate;
}

.blob-2 {
  width: 400px;
  height: 400px;
  right: -5%;
  top: 10%;
  background: radial-gradient(
    circle at center,
    rgba(218, 235, 252, 0.8),
    rgba(245, 247, 250, 0.5)
  );
  animation: blob-float-2 35s ease-in-out infinite alternate;
}

.blob-3 {
  width: 500px;
  height: 500px;
  left: 30%;
  bottom: -20%;
  background: radial-gradient(
    circle at center,
    rgba(230, 245, 252, 0.7),
    rgba(245, 247, 250, 0.4)
  );
  animation: blob-float-3 38s ease-in-out infinite alternate;
}

@keyframes blob-float-1 {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(5%, 10%) scale(1.1);
  }
}

@keyframes blob-float-2 {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(-5%, 15%) scale(0.9);
  }
}

@keyframes blob-float-3 {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(8%, -10%) scale(1.05);
  }
}

import { useState, useRef } from "react";
import { motion } from "framer-motion";

export default function SocialProof() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isPaused, setIsPaused] = useState(false);

  // Define the state flags we want to display (focused on key states for launch)
  const stateFlags = [
    { code: "TX", name: "Texas" },
    { code: "FL", name: "Florida" },
    { code: "NY", name: "New York" },
    { code: "OH", name: "Ohio" },
    { code: "IL", name: "Illinois" },
    { code: "CA", name: "California" },
    { code: "PA", name: "Pennsylvania" },
    { code: "GA", name: "Georgia" },
    { code: "NC", name: "North Carolina" },
    { code: "MI", name: "Michigan" },
    { code: "NJ", name: "New Jersey" },
    { code: "VA", name: "Virginia" },
    { code: "WA", name: "Washington" },
    { code: "AZ", name: "Arizona" },
    { code: "MA", name: "Massachusetts" },
  ];

  // Duplicate the array to create a seamless looping effect
  const duplicatedFlags = [...stateFlags, ...stateFlags];

  return (
    <section className="py-6 border-t border-b border-gray-200 overflow-hidden relative">
      {/* Fade effect on the left */}
      <div
        className="absolute left-0 top-0 h-full w-24 z-10 pointer-events-none"
        style={{
          background: "linear-gradient(90deg, white, rgba(255,255,255,0))",
        }}
      />

      {/* Fade effect on the right */}
      <div
        className="absolute right-0 top-0 h-full w-24 z-10 pointer-events-none"
        style={{
          background: "linear-gradient(270deg, white, rgba(255,255,255,0))",
        }}
      />

      <div className="relative overflow-hidden">
        <div
          ref={scrollRef}
          className="flex py-3 animate-marquee"
          style={{
            animationPlayState: isPaused ? "paused" : "running",
          }}
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          {duplicatedFlags.map((state, index) => (
            <div key={`${state.code}-${index}`} className="flex-shrink-0 mx-4">
              <motion.div
                className="bg-white rounded-lg shadow-sm px-3 py-2 border border-gray-100 flex items-center hover:shadow-md transition-all group"
                whileHover={{ y: -3, scale: 1.03 }}
                transition={{ duration: 0.2 }}
                title={state.name}
              >
                <div className="flex items-center space-x-2">
                  <div
                    className="state-pill w-8 h-6 overflow-hidden rounded-sm shadow-sm bg-gray-50 flex items-center justify-center relative 
                          group-hover:bg-primary group-hover:text-white transition-all duration-200"
                  >
                    {/* State abbreviation that changes color on hover */}
                    <div className="text-lg transition-colors duration-200">
                      {state.code}
                    </div>
                  </div>
                  <span className="text-sm font-medium text-gray-600">
                    {state.name}
                  </span>
                </div>
              </motion.div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.bubble {
  position: absolute;
  background: rgba(245, 247, 250, 0.7); /* Base color #F5F7FA with opacity */
  border-radius: 50%;
  opacity: 0.3; /* Increased opacity to be more visible */
  filter: blur(20px); /* Less blur for more definition */
  will-change: transform;
  box-shadow: 0 0 40px rgba(255, 255, 255, 0.1); /* Add a subtle glow */
}

.bubble-1 {
  width: 300px;
  height: 300px;
  left: 5%;
  bottom: -100px;
  background: linear-gradient(
    45deg,
    rgba(184, 217, 255, 0.7),
    rgba(245, 247, 250, 0.7)
  );
  animation: floatUp1 38s infinite ease-in-out;
}

.bubble-2 {
  width: 200px;
  height: 200px;
  left: 20%;
  bottom: -80px;
  background: linear-gradient(
    135deg,
    rgba(230, 245, 252, 0.7),
    rgba(205, 232, 255, 0.7)
  );
  animation: floatUp2 32s infinite ease-in-out;
}

.bubble-3 {
  width: 180px;
  height: 180px;
  left: 55%;
  bottom: -120px;
  background: linear-gradient(
    45deg,
    rgba(245, 247, 250, 0.7),
    rgba(202, 225, 255, 0.7)
  );
  animation: floatUp3 35s infinite ease-in-out 2s;
}

.bubble-4 {
  width: 350px;
  height: 350px;
  left: 70%;
  bottom: -150px;
  background: linear-gradient(
    80deg,
    rgba(230, 245, 252, 0.7),
    rgba(220, 235, 255, 0.7)
  );
  animation: floatUp4 42s infinite ease-in-out 1s;
}

.bubble-5 {
  width: 150px;
  height: 150px;
  left: 35%;
  bottom: -70px;
  background: linear-gradient(
    45deg,
    rgba(245, 247, 250, 0.7),
    rgba(218, 235, 252, 0.7)
  );
  animation: floatUp5 30s infinite ease-in-out 3s;
}

.bubble-6 {
  width: 250px;
  height: 250px;
  left: 10%;
  bottom: -120px;
  background: linear-gradient(
    45deg,
    rgba(230, 245, 252, 0.5),
    rgba(240, 250, 255, 0.5)
  );
  animation: floatUp6 37s infinite ease-in-out 2s;
}

.bubble-7 {
  width: 220px;
  height: 220px;
  left: 80%;
  bottom: -90px;
  background: linear-gradient(
    45deg,
    rgba(235, 245, 255, 0.6),
    rgba(225, 240, 255, 0.6)
  );
  animation: floatUp7 33s infinite ease-in-out 1.5s;
}

@keyframes floatUp1 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  50% {
    transform: translateY(-40vh) translateX(15px) scale(1.05);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

@keyframes floatUp2 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  50% {
    transform: translateY(-50vh) translateX(-20px) scale(1.1);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

@keyframes floatUp3 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  30% {
    transform: translateY(-30vh) translateX(10px) scale(0.95);
  }
  60% {
    transform: translateY(-60vh) translateX(-15px) scale(1.05);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

@keyframes floatUp4 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  40% {
    transform: translateY(-30vh) translateX(-25px) scale(0.9);
  }
  80% {
    transform: translateY(-60vh) translateX(5px) scale(1);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

@keyframes floatUp5 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  45% {
    transform: translateY(-45vh) translateX(30px) scale(1.15);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

@keyframes floatUp6 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  40% {
    transform: translateY(-35vh) translateX(20px) scale(0.95);
  }
  70% {
    transform: translateY(-60vh) translateX(-10px) scale(1.02);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

@keyframes floatUp7 {
  0% {
    transform: translateY(0) translateX(0) scale(1);
  }
  30% {
    transform: translateY(-30vh) translateX(-25px) scale(1.05);
  }
  60% {
    transform: translateY(-50vh) translateX(15px) scale(0.98);
  }
  100% {
    transform: translateY(0) translateX(0) scale(1);
  }
}

import { useState } from "react";
import { motion } from "framer-motion";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

type FeatureTrioProps = {
  state?: "TX" | "FL" | "NY";
};

export default function FeatureTrio({ state = "TX" }: FeatureTrioProps) {
  const [activeFeature, setActiveFeature] = useState<number | null>(null);

  const features = [
    {
      id: 1,
      code: "01",
      title: "Research Module",
      description:
        "AiLex uploads your cases, statutes, or briefs — then finds citations, creates summaries, and extracts key arguments in seconds.",
      demoTitle: "Research Demo",
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">Research Query Results</div>
          <div className="space-y-2">
            <div className="text-xs border-l-4 border-blue-400 pl-2 bg-blue-50 p-2 rounded">
              <span className="font-medium text-blue-700">
                Personal Injury Statute:
              </span>
              <div className="text-blue-600">
                {state} Civil Practice Code § 16.003 - Two-year limitation
                period
              </div>
            </div>
            <div className="text-xs border-l-4 border-green-400 pl-2 bg-green-50 p-2 rounded">
              <span className="font-medium text-green-700">Key Precedent:</span>
              <div className="text-green-600">
                Martinez v. State Farm (2023) - Comparative negligence applied
              </div>
            </div>
            <div className="text-xs border-l-4 border-purple-400 pl-2 bg-purple-50 p-2 rounded">
              <span className="font-medium text-purple-700">
                Related Cases:
              </span>
              <div className="text-purple-600">
                Found 23 similar cases in {state} courts
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 2,
      code: "02",
      title: "Drafting Co-Pilot",
      description:
        "AiLex drafts motions, pleadings, and correspondence — using state-specific language and formatting, automatically.",
      demoTitle: "Drafting Demo",
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">Motion to Compel Discovery</div>
          <p>
            Comes now the Plaintiff,{" "}
            <span className="bg-[#B8FF5C] bg-opacity-40 px-1">John Smith</span>,
            by and through counsel, and respectfully moves this Court for an
            Order compelling...
          </p>
          <div className="mt-2 text-xs bg-yellow-100 border border-yellow-200 p-2 rounded">
            <div className="font-medium text-yellow-800">AI Suggestion:</div>
            <div className="text-yellow-700">
              Add citation to {state} Rules of Civil Procedure 196.7
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 3,
      code: "03",
      title: "AI Receptionist",
      description:
        "With AiLex, you'll never miss a client call again. Our AI Receptionist answers, qualifies leads, and books appointments — 24/7.",
      demoTitle: "Voice Agent Demo",
      demoContent: (
        <>
          <div className="relative h-24 mb-4">
            <svg
              className="w-full h-full"
              viewBox="0 0 400 80"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0,40 Q10,10 20,40 T40,40 T60,40 T80,40 T100,40 T120,40 T140,40 T160,40 T180,40 T200,40 T220,40 T240,40 T260,40 T280,40 T300,40 T320,40 T340,40 T360,40 T380,40 T400,40"
                fill="none"
                stroke="white"
                strokeWidth="2"
              />
            </svg>
          </div>
          <div className="text-sm text-white mb-2">
            Sample intake conversation:
          </div>
          <div className="text-xs text-white">
            <span className="font-medium">Client:</span> I need help with my DUI
            case.
            <br />
            <span className="font-medium">AiLex:</span> I understand you need
            assistance with a DUI matter. When did this occur?
          </div>
        </>
      ),
    },
    {
      id: 4,
      code: "04",
      title: "Case Organizer",
      description:
        "AiLex keeps your cases organized — link files, notes, deadlines, and client updates in one streamlined workspace.",
      demoTitle: "Case Management Demo",
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2 text-blue-600">
            Smith v. Johnson - Personal Injury
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-green-50 border border-green-200 p-2 rounded">
              <div className="font-medium text-green-800">Files (12)</div>
              <div className="text-green-600">
                Medical records, photos, statements
              </div>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 p-2 rounded">
              <div className="font-medium text-yellow-800">Next Deadline</div>
              <div className="text-yellow-600">Discovery due in 5 days</div>
            </div>
          </div>
          <div className="mt-2 text-xs bg-blue-50 border border-blue-200 p-2 rounded">
            <div className="font-medium text-blue-800">Latest Update:</div>
            <div className="text-blue-700">
              Client called - feeling better, ready for deposition
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 5,
      code: "05",
      title: "Task & Workflow Automation",
      description:
        "From intake to resolution, AiLex keeps you on track — assign tasks, track progress, and automate the admin work you used to do manually.",
      demoTitle: "Workflow Demo",
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">New Client Workflow</div>
          <div className="space-y-2">
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="line-through text-gray-500">
                Send welcome packet
              </span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="line-through text-gray-500">
                Create case file
              </span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-blue-700 font-medium">
                Schedule initial consultation
              </span>
            </div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-3 bg-gray-300 rounded-full mr-2"></div>
              <span className="text-gray-400">Gather documents</span>
            </div>
          </div>
          <div className="mt-2 text-xs bg-green-100 border border-green-200 p-2 rounded">
            <span className="text-green-700">
              Auto-reminder sent to client ✓
            </span>
          </div>
        </div>
      ),
    },
    {
      id: 6,
      code: "06",
      title: "Deadlines & Reminders",
      description:
        "AiLex tracks deadlines, sends reminders, and syncs with your calendar — so you stay compliant, efficient, and stress-free.",
      demoTitle: "Calendar Integration Demo",
      demoContent: (
        <div className="flex-grow bg-white rounded-lg p-3 text-gray-800 text-sm overflow-hidden">
          <div className="font-medium mb-2">Upcoming Deadlines</div>
          <div className="space-y-2">
            <div className="flex justify-between items-center text-xs border-l-4 border-red-400 pl-2 bg-red-50 p-2 rounded">
              <span className="font-medium text-red-700">
                Motion Response Due
              </span>
              <span className="text-red-600">Today</span>
            </div>
            <div className="flex justify-between items-center text-xs border-l-4 border-yellow-400 pl-2 bg-yellow-50 p-2 rounded">
              <span className="font-medium text-yellow-700">
                Discovery Deadline
              </span>
              <span className="text-yellow-600">3 days</span>
            </div>
            <div className="flex justify-between items-center text-xs border-l-4 border-green-400 pl-2 bg-green-50 p-2 rounded">
              <span className="font-medium text-green-700">Client Meeting</span>
              <span className="text-green-600">1 week</span>
            </div>
          </div>
          <div className="mt-2 text-xs bg-blue-50 border border-blue-200 p-2 rounded">
            <span className="text-blue-700">
              📅 Synced with Google Calendar
            </span>
          </div>
        </div>
      ),
    },
  ];

  // Feature card variants for animations
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
      },
    }),
  };

  return (
    <section id="features" className="py-16 bg-navy text-white">
      <div className="container-content">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-12 text-center"
        >
          <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-[#B8FF5C] rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-white">
              Core Features
            </span>
          </div>
          <h2 className="text-3xl font-bold text-center">
            What AiLex Does for Solo Lawyers & Small Firms
          </h2>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8 items-stretch">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              custom={index}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={cardVariants}
              className="feature-card group flex flex-col h-full"
              onMouseEnter={() => setActiveFeature(feature.id)}
              onMouseLeave={() => setActiveFeature(null)}
            >
              <div className="code text-xs text-gray-400 mb-2">
                {feature.code}
              </div>
              <h3 className="font-bold text-xl mb-3">{feature.title}</h3>
              <div className="absolute top-6 right-6 code text-xs bg-[#B8FF5C] rounded-full px-2 py-1 text-navy">
                Ready
              </div>

              <p className="text-gray-300 mb-8">{feature.description}</p>

              <div className="mt-auto flex items-center text-primary group-hover:text-[#B8FF5C] transition-default">
                <span>See it in action</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-default"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <div
                    className={`absolute inset-0 bg-primary bg-opacity-90 p-6 flex flex-col opacity-0 transform translate-y-4 transition-default cursor-pointer ${
                      activeFeature === feature.id
                        ? "opacity-100 translate-y-0"
                        : ""
                    }`}
                  >
                    <div className="code text-xs text-navy mb-2">
                      {feature.demoTitle}
                    </div>
                    <div className="flex-grow flex flex-col justify-center">
                      {feature.demoContent}
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[650px] max-h-[80vh] overflow-y-auto bg-navy text-white">
                  <DialogHeader>
                    <DialogTitle>{feature.demoTitle}</DialogTitle>
                  </DialogHeader>
                  <div className="p-4">
                    <div className="aspect-video bg-navy bg-opacity-50 rounded-md flex flex-col items-center justify-center p-6 border border-gray-700">
                      {feature.demoContent}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <p className="text-base text-[#B8FF5C] text-center">
            <strong>Training required:</strong> 0 hours. 0 minutes. 0 seconds.
            <br />
            <em>Easy. Breezy. Ready.</em>
          </p>
        </motion.div>
      </div>
    </section>
  );
}

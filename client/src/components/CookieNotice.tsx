import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

export default function CookieNotice() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already accepted cookies
    const hasAcceptedCookies = localStorage.getItem("cookiesAccepted");

    if (!hasAcceptedCookies) {
      // Show cookie notice after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, []);

  const acceptCookies = () => {
    localStorage.setItem("cookiesAccepted", "true");
    setIsVisible(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-4 left-4 right-4 bg-white text-gray-800 p-4 rounded-xl shadow-lg md:max-w-md z-50"
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <p className="text-sm">
              We use cookies to improve your experience. By continuing, you
              agree to our{" "}
              <a href="#" className="text-primary">
                privacy policy
              </a>
              .
            </p>
            <div className="flex gap-2">
              <button
                className="text-sm text-gray-500 hover:text-gray-700 transition-default"
                onClick={() => setIsVisible(false)}
              >
                Customize
              </button>
              <button
                className="bg-primary hover:bg-opacity-90 text-white px-4 py-1 rounded-lg text-sm transition-default"
                onClick={acceptCookies}
              >
                Accept All
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

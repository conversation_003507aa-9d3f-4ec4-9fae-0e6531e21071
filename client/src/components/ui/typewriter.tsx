import React, { useState, useEffect } from "react";

interface TypewriterProps {
  text: string;
  delay?: number;
  className?: string;
  onComplete?: () => void;
}

export function Typewriter({
  text,
  delay = 50,
  className = "",
  onComplete,
}: TypewriterProps) {
  const [currentText, setCurrentText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const updateText = () => {
      try {
        if (currentIndex < text.length) {
          timeoutId = setTimeout(() => {
            setCurrentText((prevText) => prevText + text[currentIndex]);
            setCurrentIndex((prevIndex) => prevIndex + 1);
          }, delay);
        } else if (onComplete) {
          onComplete();
        }
      } catch (error) {
        console.error("Error in typewriter effect:", error);
      }
    };

    updateText();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [currentIndex, delay, text, onComplete]);

  return <span className={className}>{currentText}</span>;
}

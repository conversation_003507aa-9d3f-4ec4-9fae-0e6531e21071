import { useEffect, useState } from "react";
import { motion } from "framer-motion";

const phrases = [
  "AiLex answers your calls",
  "AiLex answers your calls, organizes deadlines",
  "AiLex answers your calls, organizes deadlines, drafts documents",
  "AiLex answers your calls, organizes deadlines, drafts documents, and finds legal precedent",
];

export default function TypewriterCTA() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState("");
  const [isTyping, setIsTyping] = useState(true);
  const [currentLetterIndex, setCurrentLetterIndex] = useState(0);

  // Handle the typing animation for the current phrase
  useEffect(() => {
    if (!isTyping) return;

    if (currentLetterIndex < phrases[currentIndex].length) {
      const typingTimer = setTimeout(() => {
        setDisplayText(
          phrases[currentIndex].substring(0, currentLetterIndex + 1)
        );
        setCurrentLetterIndex(currentLetterIndex + 1);
      }, 50); // Typing speed

      return () => clearTimeout(typingTimer);
    } else {
      setIsTyping(false);
    }
  }, [currentIndex, currentLetterIndex, isTyping]);

  // Handle progression to the next phrase
  useEffect(() => {
    if (isTyping) return;

    const progressTimer = setTimeout(() => {
      if (currentIndex < phrases.length - 1) {
        setCurrentIndex(currentIndex + 1);
        setCurrentLetterIndex(0);
        setIsTyping(true);
      } else {
        // Reset to first phrase after 3 seconds on the final phrase
        setTimeout(() => {
          setCurrentIndex(0);
          setCurrentLetterIndex(0);
          setDisplayText("");
          setIsTyping(true);
        }, 3000);
      }
    }, 1500);

    return () => clearTimeout(progressTimer);
  }, [currentIndex, isTyping]);

  return (
    <div className="w-full bg-gradient-to-b from-lime-50 to-white py-16 px-4 relative overflow-hidden">
      {/* Radial gradient background */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(184,255,92,0.2)_0%,_transparent_70%)] z-0"></div>

      <div className="max-w-5xl mx-auto text-center space-y-6 relative z-10">
        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 min-h-[100px] md:min-h-[80px] flex items-center justify-center">
          <span className="inline-block">{displayText}</span>
          <span className="inline-block w-[3px] h-8 bg-[#3B66F8] ml-1 animate-blink"></span>
        </h2>

        <p className="text-gray-600 text-lg max-w-xl mx-auto">
          AiLex researches like a librarian, drafts like an associate, and
          organizes like a paralegal — all at solo-firm prices.
        </p>

        <motion.a
          href="/login"
          className="inline-block mt-4 px-6 py-3 bg-[#3B66F8] text-white text-lg font-medium rounded-lg shadow-md hover:bg-blue-700 transition-all"
          whileHover={{
            scale: 1.05,
            boxShadow: "0 10px 25px -5px rgba(59, 102, 248, 0.4)",
            transition: { duration: 0.2 },
          }}
          whileTap={{ scale: 0.98 }}
        >
          Try AiLex Free
        </motion.a>
      </div>
    </div>
  );
}

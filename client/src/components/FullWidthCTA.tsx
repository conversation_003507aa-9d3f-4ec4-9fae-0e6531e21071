import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";

const phrases = [
  "Answers Calls",
  "Organizes Cases",
  "Drafts Memos",
  "Finds Laws",
  "Works Like an Associate",
];

export default function FullWidthCTA() {
  const [currentText, setCurrentText] = useState("");
  const [index, setIndex] = useState(0);
  const [subIndex, setSubIndex] = useState(0);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (subIndex === phrases[index].length + 1 && !deleting) {
      setTimeout(() => setDeleting(true), 1500);
      return;
    }

    if (subIndex === 0 && deleting) {
      setDeleting(false);
      setIndex((prev) => (prev + 1) % phrases.length);
      return;
    }

    const timeout = setTimeout(
      () => {
        setSubIndex((prev) => prev + (deleting ? -1 : 1));
      },
      deleting ? 30 : 70
    );

    return () => clearTimeout(timeout);
  }, [subIndex, index, deleting]);

  useEffect(() => {
    setCurrentText(phrases[index].substring(0, subIndex));
  }, [subIndex, index]);

  return (
    <section className="relative w-full py-24 overflow-hidden bg-white">
      {/* Top-left blob */}
      <div className="absolute top-0 left-0 w-[400px] h-[400px] rounded-full z-[1] bg-[radial-gradient(circle,_rgba(184,255,92,0.15)_0%,_transparent_70%)] blur-3xl" />

      {/* Bottom-right blob */}
      <div className="absolute bottom-0 right-0 w-[400px] h-[400px] rounded-full z-[1] bg-[radial-gradient(circle,_rgba(184,255,92,0.18)_0%,_transparent_70%)] blur-3xl" />

      <div className="max-w-6xl mx-auto px-6 relative z-10">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="min-h-[80px] flex items-center justify-center">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              <span>{currentText}</span>
              <span className="border-r-4 border-blue-500 ml-1 h-12 animate-pulse"></span>
            </h2>
          </div>

          <motion.p
            className="text-xl md:text-2xl text-gray-700 mt-6 max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            AiLex acts like your full-time assistant—minus the overhead.
          </motion.p>

          <motion.div
            className="mt-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.a
              href="/login"
              className="inline-block px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-lg font-bold rounded-xl shadow-xl hover:shadow-blue-500/30 cursor-pointer"
              whileHover={{
                scale: 1.05,
                boxShadow: "0 15px 30px -5px rgba(59, 130, 246, 0.4)",
                transition: { duration: 0.2 },
              }}
              whileTap={{ scale: 0.98 }}
            >
              Start Free Trial
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}

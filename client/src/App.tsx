import { Switch, Route } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

import Home from "@/pages/Home";
import StatePage from "@/pages/StatePage";
import MarketBlogRouter from "@/components/MarketBlogRouter";

import Login from "@/pages/Login";
import SecurityWhitepaper from "@/pages/SecurityWhitepaper";
import AdminLogin from "@/pages/AdminLogin";
import AdminForgotPassword from "@/pages/AdminForgotPassword";
import AdminResetPassword from "@/pages/AdminResetPassword";
import AdminDashboard from "@/pages/AdminDashboard";
import AdminBlogArticles from "@/pages/AdminBlogArticles";
import AdminBlogEditor from "@/pages/AdminBlogEditor";
import AdminBlogCategories from "@/pages/AdminBlogCategories";
import AdminBlogTranslations from "@/pages/AdminBlogTranslations";
import AdminImageGallery from "@/pages/AdminImageGallery";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/tx" component={() => <StatePage state="TX" />} />
      <Route path="/fl" component={() => <StatePage state="FL" />} />
      <Route path="/ny" component={() => <StatePage state="NY" />} />
      {/* Blog routes with market support */}
      <Route path="/blog" component={MarketBlogRouter} />
      <Route path="/blog/:slug" component={MarketBlogRouter} />
      <Route path="/blog/usa" component={MarketBlogRouter} />
      <Route path="/blog/usa/:slug" component={MarketBlogRouter} />
      <Route path="/blog/belgium-fr" component={MarketBlogRouter} />
      <Route path="/blog/belgium-fr/:slug" component={MarketBlogRouter} />
      <Route path="/blog/belgium-nl" component={MarketBlogRouter} />
      <Route path="/blog/belgium-nl/:slug" component={MarketBlogRouter} />
      <Route path="/login" component={Login} />
      <Route path="/security-whitepaper" component={SecurityWhitepaper} />
      <Route path="/admin-secret-portal" component={AdminLogin} />
      <Route
        path="/admin-secret-portal/forgot-password"
        component={AdminForgotPassword}
      />
      <Route
        path="/admin-secret-portal/reset-password/:token"
        component={AdminResetPassword}
      />
      <Route path="/admin-secret-portal/dashboard" component={AdminDashboard} />
      <Route
        path="/admin-secret-portal/dashboard/blog/articles"
        component={AdminBlogArticles}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/articles/new"
        component={AdminBlogEditor}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/articles/:id/edit"
        component={AdminBlogEditor}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/categories"
        component={AdminBlogCategories}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/translations"
        component={AdminBlogTranslations}
      />
      <Route
        path="/admin-secret-portal/dashboard/images"
        component={AdminImageGallery}
      />
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;

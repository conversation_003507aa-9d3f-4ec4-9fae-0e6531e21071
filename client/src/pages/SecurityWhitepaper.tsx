import { <PERSON> } from "wouter";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { motion } from "framer-motion";

export default function SecurityWhitepaper() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation back */}
      <div className="max-w-6xl mx-auto px-6 py-6">
        <Link
          href="/"
          className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Link>
      </div>

      <section className="max-w-3xl mx-auto px-6 py-16 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            AiLex Security Overview
          </h1>
          <p className="text-gray-600 text-lg leading-relaxed mb-8">
            We know legal work demands privacy, security, and total control over
            client data. AiLex was built from the ground up to meet those
            standards — and exceed them.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left text-gray-700 mb-10">
            <motion.div
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="flex items-center mb-3">
                <Lock className="w-5 h-5 text-[#1EAEDB] mr-2" />
                <h3 className="font-semibold text-gray-900">Encryption</h3>
              </div>
              <p>
                All data is encrypted in transit (TLS 1.3) and at rest
                (AES-256), with per-tenant keys managed by Google Cloud KMS.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="flex items-center mb-3">
                <UserCheck className="w-5 h-5 text-[#1EAEDB] mr-2" />
                <h3 className="font-semibold text-gray-900">
                  Zero Admin Access
                </h3>
              </div>
              <p>
                Even our system administrators cannot access your documents or
                client data. Full control stays with your firm.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="flex items-center mb-3">
                <Shield className="w-5 h-5 text-[#1EAEDB] mr-2" />
                <h3 className="font-semibold text-gray-900">
                  SOC-2 Infrastructure
                </h3>
              </div>
              <p>
                AiLex is built on SOC-2 certified infrastructure. Full SOC-2
                compliance is on our roadmap.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center mb-3">
                <Key className="w-5 h-5 text-[#1EAEDB] mr-2" />
                <h3 className="font-semibold text-gray-900">Ethical AI</h3>
              </div>
              <p>
                We never train our models on your client data — not now, not
                ever. Your data is always yours.
              </p>
            </motion.div>
          </div>

          <motion.div
            className="bg-blue-50 border border-blue-200 rounded-xl p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <p className="text-gray-700 mb-4">
              Want a copy of our full whitepaper when it's ready?
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center text-[#1EAEDB] hover:text-[#0C1C2D] font-medium transition-colors"
            >
              Contact us here
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                />
              </svg>
            </a>
          </motion.div>
        </motion.div>
      </section>

      {/* Footer navigation back */}
      <div className="max-w-6xl mx-auto px-6 pb-8">
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}

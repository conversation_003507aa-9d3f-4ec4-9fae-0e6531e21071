import { useState, useEffect } from "react";
import { Link } from "wouter";
import { motion } from "framer-motion";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";

interface BlogPost {
  id: number;
  title: string;
  summary: string;
  slug: string;
  featured_image_url?: string;
  read_time_minutes?: number;
  published_at: string;
  created_at: string;
  market: string;
  language: string;
  blog_categories?: {
    id: number;
    name: string;
    slug: string;
    color: string;
  };
  website_admins: {
    id: number;
    name: string;
  };
  _resolution?: {
    source: string;
    translationUsed: boolean;
  };
}

interface BlogProps {
  market?: string;
  language?: string;
}

export default function Blog({
  market = "global",
  language = "en",
}: BlogProps) {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getMarketPath = (market: string): string => {
    const marketPaths: Record<string, string> = {
      global: "/blog",
      usa: "/blog/usa",
      "be-fr": "/blog/belgium-fr",
      "be-nl": "/blog/belgium-nl",
    };
    return marketPaths[market] || "/blog";
  };

  const getBlogPostUrl = (slug: string): string => {
    const basePath = getMarketPath(market);
    return `${basePath}/${slug}`;
  };

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        const response = await fetch(
          `/api/blog/articles/market/${market}?language=${language}&limit=20`
        );
        if (response.ok) {
          const data = await response.json();
          setBlogPosts(data.articles);
        } else {
          setError("Failed to load blog articles");
        }
      } catch (err) {
        console.error("Error fetching blog posts:", err);
        setError("Failed to load blog articles");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogPosts();
  }, [market, language]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatReadTime = (minutes?: number) => {
    if (!minutes) return "5 min read";
    return `${minutes} min read`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <Navbar />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading articles...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <Navbar />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar />
      {/* Hero Section */}
      <section className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-8"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Back to Home
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-center"
          >
            <Badge className="mb-6 bg-blue-100 text-blue-700 hover:bg-blue-200">
              <BookOpen className="w-4 h-4 mr-2" />
              Legal Insights
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Resources for Solo Practitioners & Small Firms
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              Practical insights, proven strategies, and expert guidance to help
              you build a successful legal practice.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20">
        <div className="max-w-6xl mx-auto px-6">
          {blogPosts.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-gray-400 mb-4">
                <BookOpen className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                No articles yet
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                We're working on creating valuable content for legal
                professionals. Check back soon for insights and tips!
              </p>
              <Link href="/">
                <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
                  <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                  Back to Home
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid gap-8 md:gap-10">
              {blogPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.12)] transition-all duration-300 p-8 border border-white/20"
                >
                  <div className="flex flex-col lg:flex-row gap-6">
                    {/* Featured Image */}
                    {post.featured_image_url && (
                      <div className="lg:w-80 lg:flex-shrink-0">
                        <div className="aspect-video lg:aspect-square rounded-xl overflow-hidden bg-gray-100">
                          <img
                            src={post.featured_image_url}
                            alt={post.title}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                            onError={(e) => {
                              e.currentTarget.style.display = "none";
                            }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        {post.blog_categories && (
                          <Badge
                            variant="outline"
                            className="border-blue-200"
                            style={{
                              color: post.blog_categories.color,
                              borderColor: post.blog_categories.color + "40",
                            }}
                          >
                            {post.blog_categories.name}
                          </Badge>
                        )}
                        <div className="flex items-center text-sm text-gray-500 gap-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {formatDate(post.published_at)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {formatReadTime(post.read_time_minutes)}
                          </div>
                        </div>
                      </div>

                      <h2 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">
                        {post.title}
                      </h2>

                      <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                        {post.summary}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          By {post.website_admins.name}
                        </span>
                        <Link href={getBlogPostUrl(post.slug)}>
                          <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white px-6 py-3 group">
                            Read Article
                            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </motion.article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-gradient-to-r from-[#0C1C2D] to-[#1a2b3d] rounded-2xl p-8 md:p-12 text-center text-white"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Transform Your Practice?
            </h3>
            <p className="text-blue-100 mb-8 text-lg max-w-2xl mx-auto">
              Join thousands of attorneys who are already using AiLex to
              streamline their legal research and grow their practice.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/login">
                <Button className="bg-[#B8FF5C] hover:bg-[#B8FF5C]/90 text-[#0C1C2D] font-semibold px-8 py-3">
                  Start Your Free Trial
                </Button>
              </Link>
              <Link
                href="/"
                className="inline-flex items-center text-blue-100 hover:text-white transition-colors"
              >
                <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                Back to Home
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

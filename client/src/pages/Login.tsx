import { useState } from "react";
import { Link, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowRight, Mail, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Navbar from "@/components/Navbar";

export default function Login() {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [, setLocation] = useLocation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!firstName || !lastName || !email) return;

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
    }, 1000);
  };

  const handleNavigateToSection = (sectionId: string) => {
    setLocation("/");
    setTimeout(() => {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }, 100);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F5F7FA] via-white to-[#1EAEDB]/5">
      <Navbar />

      <div className="pt-24 pb-20">
        <div className="max-w-2xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            {/* Back to Home Link */}
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-12"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Back to Home
            </Link>

            {!isSubmitted ? (
              <>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="mb-8"
                >
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Login Coming Soon
                  </h1>
                  <p className="text-xl text-gray-600 leading-relaxed max-w-lg mx-auto">
                    We're gradually rolling out access to AiLex. Want early
                    access? Drop your email below.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] p-8 border border-white/20 max-w-md mx-auto"
                >
                  <div className="flex items-center justify-center mb-6">
                    <div className="bg-[#1EAEDB]/10 p-3 rounded-full">
                      <Mail className="w-6 h-6 text-[#1EAEDB]" />
                    </div>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* First Name and Last Name - Side by side on desktop */}
                    <div className="flex flex-col md:flex-row md:gap-4 space-y-4 md:space-y-0">
                      <div className="flex-1">
                        <label
                          htmlFor="firstName"
                          className="block text-sm font-medium text-gray-700 mb-1 text-left"
                        >
                          First Name
                        </label>
                        <Input
                          id="firstName"
                          type="text"
                          placeholder="First name"
                          value={firstName}
                          onChange={(e) => setFirstName(e.target.value)}
                          required
                          className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors"
                        />
                      </div>
                      <div className="flex-1">
                        <label
                          htmlFor="lastName"
                          className="block text-sm font-medium text-gray-700 mb-1 text-left"
                        >
                          Last Name
                        </label>
                        <Input
                          id="lastName"
                          type="text"
                          placeholder="Last name"
                          value={lastName}
                          onChange={(e) => setLastName(e.target.value)}
                          required
                          className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors"
                        />
                      </div>
                    </div>

                    {/* Email Address */}
                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium text-gray-700 mb-1 text-left"
                      >
                        Email Address
                      </label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors"
                      />
                    </div>

                    {/* Optional Message */}
                    <div>
                      <label
                        htmlFor="message"
                        className="block text-sm font-medium text-gray-700 mb-1 text-left"
                      >
                        What would you like AiLex to help with?{" "}
                        <span className="text-gray-500 font-normal">
                          (optional)
                        </span>
                      </label>
                      <textarea
                        id="message"
                        placeholder="Tell us about your practice and what you'd like AiLex to help with..."
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        rows={3}
                        className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors resize-none"
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isLoading || !firstName || !lastName || !email}
                      style={{
                        backgroundColor:
                          isLoading || !firstName || !lastName || !email
                            ? "#87ceeb"
                            : "#1EAEDB",
                        background:
                          isLoading || !firstName || !lastName || !email
                            ? "#87ceeb"
                            : "#1EAEDB",
                        color: "white",
                        width: "100%",
                        padding: "12px 16px",
                        fontSize: "18px",
                        fontWeight: "600",
                        borderRadius: "6px",
                        border: "none",
                        cursor:
                          isLoading || !firstName || !lastName || !email
                            ? "not-allowed"
                            : "pointer",
                        transition: "all 0.2s",
                      }}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center">
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                          Submitting...
                        </div>
                      ) : (
                        "Request Early Access"
                      )}
                    </button>
                  </form>

                  <p className="text-sm text-gray-500 mt-4 text-center">
                    We'll notify you as soon as login is available
                  </p>

                  {/* Accent lime highlight */}
                  <div className="flex items-center justify-center mt-2">
                    <div
                      className="w-8 h-1 rounded-full"
                      style={{ backgroundColor: "#B8FF5C" }}
                    ></div>
                  </div>
                </motion.div>
              </>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] p-8 border border-white/20 max-w-md mx-auto text-center"
              >
                <div className="flex items-center justify-center mb-6">
                  <div className="bg-[#B8FF5C]/20 p-3 rounded-full">
                    <CheckCircle className="w-6 h-6 text-[#0C1C2D]" />
                  </div>
                </div>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  You're on the list!
                </h2>

                {/* Accent lime highlight for success */}
                <div className="flex items-center justify-center mb-4">
                  <div
                    className="w-12 h-1 rounded-full"
                    style={{ backgroundColor: "#B8FF5C" }}
                  ></div>
                </div>
                <p className="text-gray-600 mb-6">
                  Thanks {firstName}! We'll email you at{" "}
                  <strong>{email}</strong> as soon as AiLex login is ready.
                </p>

                <Link href="/">
                  <Button
                    className="bg-[#1EAEDB] hover:bg-[#1EAEDB]/90 text-white px-6 py-3 border-0"
                    style={{ backgroundColor: "#1EAEDB" }}
                  >
                    Back to Home
                  </Button>
                </Link>
              </motion.div>
            )}

            {/* Additional Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mt-12 text-center"
            >
              <p className="text-gray-500 mb-4">
                While you wait, explore what AiLex can do for your practice
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  className="border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto"
                  onClick={() => handleNavigateToSection("features")}
                >
                  See Features
                </Button>
                <Button
                  variant="outline"
                  className="border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto"
                  onClick={() => handleNavigateToSection("pricing")}
                >
                  View Pricing
                </Button>
                <Button
                  variant="outline"
                  className="border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto"
                  onClick={() => setLocation("/blog")}
                >
                  Read Blog
                </Button>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

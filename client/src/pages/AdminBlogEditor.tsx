import { useState, useEffect } from "react";
import { useLocation, useRoute } from "wouter";
import { motion } from "framer-motion";
import {
  Save,
  ArrowLeft,
  Eye,
  EyeOff,
  Calendar,
  Tag,
  Image,
  Type,
  FileText,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { verifyAdminToken, type AdminUser } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import ImageUpload from "@/components/ImageUpload";

interface BlogArticle {
  id?: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  categoryId?: number;
  status: "draft" | "published" | "archived";
  featuredImageUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  readTimeMinutes?: number;
  publishedAt?: string;
  // Multi-market fields
  market: "global" | "usa" | "be-fr" | "be-nl";
  language: "en" | "fr" | "nl";
  translationKeyId?: string;
  isTranslation?: boolean;
  baseArticleId?: number;
}

interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  color: string;
}

interface BlogMarket {
  marketCode: string;
  marketName: string;
  languageCode: string;
  languageName: string;
  urlPath: string;
  isActive: boolean;
}

type MarketType = "global" | "usa" | "be-fr" | "be-nl";
type LanguageType = "en" | "fr" | "nl";

export default function AdminBlogEditor() {
  const [, setLocation] = useLocation();
  const [, params] = useRoute(
    "/admin-secret-portal/dashboard/blog/articles/:id/edit"
  );
  const [, newParams] = useRoute(
    "/admin-secret-portal/dashboard/blog/articles/new"
  );
  const { toast } = useToast();

  const isEditing = !!params?.id;
  const articleId = params?.id;

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  const [article, setArticle] = useState<BlogArticle>({
    title: "",
    slug: "",
    summary: "",
    content: "",
    status: "draft",
    featuredImageUrl: "",
    metaTitle: "",
    metaDescription: "",
    market: "global",
    language: "en",
    isTranslation: false,
  });

  const [markets, setMarkets] = useState<BlogMarket[]>([]);
  const [availableLanguages, setAvailableLanguages] = useState<LanguageType[]>([
    "en",
  ]);

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem("adminToken");
      if (!token) {
        setLocation("/admin-secret-portal");
        return;
      }

      try {
        const user = await verifyAdminToken(token);
        if (user) {
          setAdminUser(user);
          setIsAuthenticated(true);
          await fetchCategories();
          await fetchMarkets();
          if (isEditing && articleId) {
            await fetchArticle(articleId);
          }
        } else {
          localStorage.removeItem("adminToken");
          localStorage.removeItem("adminUser");
          setLocation("/admin-secret-portal");
        }
      } catch (error) {
        console.error("Auth verification failed:", error);
        setLocation("/admin-secret-portal");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [setLocation, isEditing, articleId]);

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/blog/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchMarkets = async () => {
    try {
      const response = await fetch("/api/blog/markets");
      if (response.ok) {
        const data = await response.json();
        setMarkets(data);

        // Update available languages based on current market
        updateAvailableLanguages(article.market, data);
      }
    } catch (error) {
      console.error("Error fetching markets:", error);
    }
  };

  const updateAvailableLanguages = (
    market: MarketType,
    marketData: BlogMarket[]
  ) => {
    const marketInfo = marketData.filter((m) => m.marketCode === market);
    if (marketInfo.length > 0) {
      const languages = Array.from(
        new Set(marketInfo.map((m) => m.languageCode))
      ) as LanguageType[];
      setAvailableLanguages(languages);
    } else {
      setAvailableLanguages(["en"]);
    }
  };

  const fetchArticle = async (id: string) => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch(`/api/admin/blog/articles/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const fetchedArticle = data.article;
        setArticle({
          id: fetchedArticle.id,
          title: fetchedArticle.title,
          slug: fetchedArticle.slug,
          summary: fetchedArticle.summary || "",
          content: fetchedArticle.content,
          categoryId: fetchedArticle.category_id,
          status: fetchedArticle.status,
          featuredImageUrl: fetchedArticle.featured_image_url || "",
          metaTitle: fetchedArticle.meta_title || "",
          metaDescription: fetchedArticle.meta_description || "",
          readTimeMinutes: fetchedArticle.read_time_minutes,
          publishedAt: fetchedArticle.published_at,
          market: fetchedArticle.market || "global",
          language: fetchedArticle.language || "en",
          translationKeyId: fetchedArticle.translation_key_id,
          isTranslation: fetchedArticle.is_translation || false,
          baseArticleId: fetchedArticle.base_article_id,
        });

        // Update available languages for the fetched article's market
        updateAvailableLanguages(fetchedArticle.market || "global", markets);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch article",
          variant: "destructive",
        });
        setLocation("/admin-secret-portal/dashboard/blog/articles");
      }
    } catch (error) {
      console.error("Error fetching article:", error);
      toast({
        title: "Error",
        description: "Failed to fetch article",
        variant: "destructive",
      });
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setArticle((prev) => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title),
      metaTitle: prev.metaTitle || title,
    }));
  };

  const handleMarketChange = (market: MarketType) => {
    setArticle((prev) => ({
      ...prev,
      market,
      // Reset language to the first available for this market
      language: getDefaultLanguageForMarket(market),
    }));
    updateAvailableLanguages(market, markets);
  };

  const handleLanguageChange = (language: LanguageType) => {
    setArticle((prev) => ({
      ...prev,
      language,
    }));
  };

  const getDefaultLanguageForMarket = (market: MarketType): LanguageType => {
    const marketDefaults = {
      global: "en" as LanguageType,
      usa: "en" as LanguageType,
      "be-fr": "fr" as LanguageType,
      "be-nl": "nl" as LanguageType,
    };
    return marketDefaults[market];
  };

  const getMarketDisplayName = (market: MarketType): string => {
    const marketNames = {
      global: "Global",
      usa: "United States",
      "be-fr": "Belgium (French)",
      "be-nl": "Belgium (Dutch)",
    };
    return marketNames[market];
  };

  const getLanguageDisplayName = (language: LanguageType): string => {
    const languageNames = {
      en: "English",
      fr: "Français",
      nl: "Nederlands",
    };
    return languageNames[language];
  };

  const handleSave = async (status?: "draft" | "published" | "archived") => {
    setIsSaving(true);
    try {
      const token = localStorage.getItem("adminToken");
      const articleData = {
        ...article,
        ...(status && { status }),
      };

      const url = isEditing
        ? `/api/admin/blog/articles/${articleId}`
        : "/api/admin/blog/articles";

      const method = isEditing ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(articleData),
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Success",
          description: data.message,
        });

        if (!isEditing) {
          setLocation(
            `/admin-secret-portal/dashboard/blog/articles/${data.article.id}/edit`
          );
        } else {
          // Update local state with saved data
          setArticle((prev) => ({ ...prev, ...data.article }));
        }
      } else {
        const errorData = await response.json();
        toast({
          title: "Error",
          description: errorData.message || "Failed to save article",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error saving article:", error);
      toast({
        title: "Error",
        description: "Failed to save article",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const calculateReadingTime = (content: string) => {
    const plainText = content.replace(/<[^>]*>/g, "");
    const wordCount = plainText
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
    return Math.max(1, Math.round(wordCount / 200));
  };

  if (isLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">
            {isLoading ? "Loading..." : "Authenticating..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  setLocation("/admin-secret-portal/dashboard/blog/articles")
                }
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Articles</span>
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">
                {isEditing ? "Edit Article" : "New Article"}
              </h1>
              {article.status && (
                <Badge
                  className={
                    article.status === "published"
                      ? "bg-green-100 text-green-800"
                      : article.status === "draft"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-gray-100 text-gray-800"
                  }
                >
                  {article.status.charAt(0).toUpperCase() +
                    article.status.slice(1)}
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
                className="flex items-center space-x-2"
              >
                {showPreview ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
                <span>{showPreview ? "Hide Preview" : "Show Preview"}</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => handleSave("draft")}
                disabled={isSaving}
                className="flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>Save Draft</span>
              </Button>
              <Button
                onClick={() => handleSave("published")}
                disabled={isSaving}
                className="flex items-center space-x-2"
              >
                <Calendar className="w-4 h-4" />
                <span>Publish</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Editor */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="w-5 h-5" />
                    <span>Article Content</span>
                  </CardTitle>
                  <CardDescription>
                    Write and format your blog article content
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="title">Title *</Label>
                    <Input
                      id="title"
                      value={article.title}
                      onChange={(e) => handleTitleChange(e.target.value)}
                      placeholder="Enter article title..."
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="slug">URL Slug *</Label>
                    <Input
                      id="slug"
                      value={article.slug}
                      onChange={(e) =>
                        setArticle((prev) => ({
                          ...prev,
                          slug: e.target.value,
                        }))
                      }
                      placeholder="article-url-slug"
                      className="mt-1"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      URL: /blog/{article.slug || "article-slug"}
                    </p>
                  </div>

                  {/* Market and Language Selection */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="market">Target Market *</Label>
                      <Select
                        value={article.market}
                        onValueChange={(value) =>
                          handleMarketChange(value as MarketType)
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select market" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="global">🌍 Global</SelectItem>
                          <SelectItem value="usa">🇺🇸 United States</SelectItem>
                          <SelectItem value="be-fr">
                            🇧🇪 Belgium (French)
                          </SelectItem>
                          <SelectItem value="be-nl">
                            🇧🇪 Belgium (Dutch)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-gray-500 mt-1">
                        Choose the target market for this article
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="language">Language *</Label>
                      <Select
                        value={article.language}
                        onValueChange={(value) =>
                          handleLanguageChange(value as LanguageType)
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableLanguages.map((lang) => (
                            <SelectItem key={lang} value={lang}>
                              {getLanguageDisplayName(lang)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-gray-500 mt-1">
                        Content language for this article
                      </p>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="summary">Summary</Label>
                    <Textarea
                      id="summary"
                      value={article.summary}
                      onChange={(e) =>
                        setArticle((prev) => ({
                          ...prev,
                          summary: e.target.value,
                        }))
                      }
                      placeholder="Brief summary of the article..."
                      rows={3}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="content">Content *</Label>
                    <Textarea
                      id="content"
                      value={article.content}
                      onChange={(e) =>
                        setArticle((prev) => ({
                          ...prev,
                          content: e.target.value,
                        }))
                      }
                      placeholder="Write your article content here... You can use HTML tags for formatting."
                      rows={20}
                      className="mt-1 font-mono text-sm"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Estimated reading time:{" "}
                      {calculateReadingTime(article.content)} min
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              {/* Article Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Tag className="w-5 h-5" />
                    <span>Article Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={article.categoryId?.toString() || ""}
                      onValueChange={(value) =>
                        setArticle((prev) => ({
                          ...prev,
                          categoryId: value ? parseInt(value) : undefined,
                        }))
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: category.color }}
                              />
                              <span>{category.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={article.status}
                      onValueChange={(
                        value: "draft" | "published" | "archived"
                      ) => setArticle((prev) => ({ ...prev, status: value }))}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <ImageUpload
                    value={article.featuredImageUrl}
                    onChange={(url) =>
                      setArticle((prev) => ({
                        ...prev,
                        featuredImageUrl: url,
                      }))
                    }
                    onRemove={() =>
                      setArticle((prev) => ({
                        ...prev,
                        featuredImageUrl: "",
                      }))
                    }
                  />
                </CardContent>
              </Card>

              {/* SEO Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Type className="w-5 h-5" />
                    <span>SEO Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="metaTitle">Meta Title</Label>
                    <Input
                      id="metaTitle"
                      value={article.metaTitle}
                      onChange={(e) =>
                        setArticle((prev) => ({
                          ...prev,
                          metaTitle: e.target.value,
                        }))
                      }
                      placeholder="SEO title for search engines"
                      className="mt-1"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {article.metaTitle?.length || 0}/60 characters
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="metaDescription">Meta Description</Label>
                    <Textarea
                      id="metaDescription"
                      value={article.metaDescription}
                      onChange={(e) =>
                        setArticle((prev) => ({
                          ...prev,
                          metaDescription: e.target.value,
                        }))
                      }
                      placeholder="Brief description for search engines"
                      rows={3}
                      className="mt-1"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {article.metaDescription?.length || 0}/160 characters
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Preview */}
              {showPreview && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Eye className="w-5 h-5" />
                      <span>Preview</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-sm max-w-none">
                      <h1 className="text-xl font-bold mb-2">
                        {article.title || "Article Title"}
                      </h1>
                      {article.summary && (
                        <p className="text-gray-600 mb-4">{article.summary}</p>
                      )}
                      <div
                        className="prose-content"
                        dangerouslySetInnerHTML={{
                          __html:
                            article.content ||
                            "<p>Article content will appear here...</p>",
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </div>
        </div>
      </main>
    </div>
  );
}

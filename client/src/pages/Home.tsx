import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import SocialProof from "@/components/SocialProof";
import ProblemSolution from "@/components/ProblemSolution";
import FeatureTrio from "@/components/FeatureTrio";
import RoiCalculator from "@/components/RoiCalculator";
import FloatingNav from "@/components/FloatingNav";
import Testimonials from "@/components/Testimonials";
import IndustryInsights from "@/components/IndustryInsights";
import Pricing from "@/components/Pricing";
import SecurityBanner from "@/components/SecurityBanner";
import Footer from "@/components/Footer";
import CookieNotice from "@/components/CookieNotice";

export default function Home() {
  // Set page title
  useEffect(() => {
    document.title = "AiLex - Your AI Legal Associate";
  }, []);

  return (
    <div className="relative overflow-x-hidden">
      <Navbar />
      <FloatingNav />
      <main>
        <div id="hero">
          <Hero />
        </div>
        <SocialProof />
        <ProblemSolution />
        <FeatureTrio />
        <RoiCalculator />
        <IndustryInsights />
        {/* <Testimonials /> */}
        <div id="pricing">
          <Pricing />
        </div>
        <SecurityBanner />
      </main>
      <Footer />
      <CookieNotice />

      {/* Mobile action button */}
      <div className="fixed md:hidden bottom-6 right-6 z-40">
        <button className="bg-primary text-white h-14 w-14 rounded-full shadow-lg flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        </button>
      </div>
    </div>
  );
}

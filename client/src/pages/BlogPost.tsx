import { useState, useEffect } from "react";
import { Link, useRoute } from "wouter";
import { motion } from "framer-motion";
import DOMPurify from "dompurify";
import {
  Calendar,
  Clock,
  ArrowLeft,
  BookOpen,
  CheckCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";

// SEO Meta Tags Component
function SEOHead({
  post,
  market,
  language,
}: {
  post: BlogPostData;
  market: string;
  language: string;
}) {
  useEffect(() => {
    // Update document title
    document.title = post.meta_title || `${post.title} | AiLex Blog`;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        post.meta_description || post.summary
      );
    } else {
      const meta = document.createElement("meta");
      meta.name = "description";
      meta.content = post.meta_description || post.summary;
      document.head.appendChild(meta);
    }

    // Update Open Graph tags
    const updateOrCreateMeta = (property: string, content: string) => {
      let meta = document.querySelector(`meta[property="${property}"]`);
      if (meta) {
        meta.setAttribute("content", content);
      } else {
        meta = document.createElement("meta");
        meta.setAttribute("property", property);
        meta.setAttribute("content", content);
        document.head.appendChild(meta);
      }
    };

    // Generate market-specific URL
    const getMarketPath = (market: string): string => {
      const marketPaths: Record<string, string> = {
        global: "/blog",
        usa: "/blog/usa",
        "be-fr": "/blog/belgium-fr",
        "be-nl": "/blog/belgium-nl",
      };
      return marketPaths[market] || "/blog";
    };

    const marketSpecificUrl = `${window.location.origin}${getMarketPath(market)}/${post.slug}`;

    updateOrCreateMeta("og:title", post.meta_title || post.title);
    updateOrCreateMeta("og:description", post.meta_description || post.summary);
    updateOrCreateMeta("og:type", "article");
    updateOrCreateMeta("og:url", marketSpecificUrl);
    updateOrCreateMeta(
      "og:locale",
      language === "fr" ? "fr_FR" : language === "nl" ? "nl_NL" : "en_US"
    );
    if (post.featured_image_url) {
      updateOrCreateMeta("og:image", post.featured_image_url);
    }

    // Update Twitter Card tags
    const updateOrCreateTwitterMeta = (name: string, content: string) => {
      let meta = document.querySelector(`meta[name="${name}"]`);
      if (meta) {
        meta.setAttribute("content", content);
      } else {
        meta = document.createElement("meta");
        meta.setAttribute("name", name);
        meta.setAttribute("content", content);
        document.head.appendChild(meta);
      }
    };

    updateOrCreateTwitterMeta("twitter:card", "summary_large_image");
    updateOrCreateTwitterMeta("twitter:title", post.meta_title || post.title);
    updateOrCreateTwitterMeta(
      "twitter:description",
      post.meta_description || post.summary
    );
    if (post.featured_image_url) {
      updateOrCreateTwitterMeta("twitter:image", post.featured_image_url);
    }

    // Add hreflang tags for international SEO
    const addHreflangTag = (hreflang: string, href: string) => {
      // Remove existing hreflang tag if it exists
      const existing = document.querySelector(`link[hreflang="${hreflang}"]`);
      if (existing) {
        existing.remove();
      }

      const link = document.createElement("link");
      link.rel = "alternate";
      link.hreflang = hreflang;
      link.href = href;
      document.head.appendChild(link);
    };

    // Add hreflang tags for all available markets
    const baseUrl = window.location.origin;
    addHreflangTag("en", `${baseUrl}/blog/${post.slug}`); // Global/default
    addHreflangTag("en-US", `${baseUrl}/blog/usa/${post.slug}`); // USA
    addHreflangTag("fr-BE", `${baseUrl}/blog/belgium-fr/${post.slug}`); // Belgium French
    addHreflangTag("nl-BE", `${baseUrl}/blog/belgium-nl/${post.slug}`); // Belgium Dutch
    addHreflangTag("x-default", `${baseUrl}/blog/${post.slug}`); // Default fallback

    // Add JSON-LD structured data
    const existingScript = document.querySelector(
      'script[type="application/ld+json"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const structuredData = {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      headline: post.title,
      description: post.summary,
      url: marketSpecificUrl,
      inLanguage:
        language === "fr" ? "fr-BE" : language === "nl" ? "nl-BE" : "en-US",
      author: {
        "@type": "Person",
        name: post.website_admins.name,
      },
      datePublished: post.published_at,
      dateModified: post.created_at,
      publisher: {
        "@type": "Organization",
        name: "AiLex",
        logo: {
          "@type": "ImageObject",
          url: `${window.location.origin}/logo.png`,
        },
      },
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": `${window.location.origin}/blog/${post.slug}`,
      },
      ...(post.featured_image_url && {
        image: {
          "@type": "ImageObject",
          url: post.featured_image_url,
        },
      }),
      ...(post.blog_categories && {
        articleSection: post.blog_categories.name,
      }),
    };

    const script = document.createElement("script");
    script.type = "application/ld+json";
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      // Reset title to default
      document.title = "AiLex - AI-Powered Legal Assistant";
    };
  }, [post]);

  return null;
}

interface BlogPostData {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  read_time_minutes?: number;
  published_at: string;
  created_at: string;
  market: string;
  language: string;
  blog_categories?: {
    id: number;
    name: string;
    slug: string;
    color: string;
  };
  website_admins: {
    id: number;
    name: string;
  };
  _resolution?: {
    source: string;
    translationUsed: boolean;
  };
}

interface BlogPostProps {
  market?: string;
  language?: string;
  slug?: string;
}

// Dynamic blog post data is now fetched from API

export default function BlogPost({
  market = "global",
  language = "en",
  slug: propSlug,
}: BlogPostProps) {
  const [, params] = useRoute("/blog/:slug");
  const slug = propSlug || params?.slug;

  const [post, setPost] = useState<BlogPostData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPost = async () => {
      if (!slug) {
        setError("No article slug provided");
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch(
          `/api/blog/articles/market/${market}/${slug}?language=${language}`
        );
        if (response.ok) {
          const data = await response.json();
          setPost(data);
        } else if (response.status === 404) {
          setError("Article not found");
        } else {
          setError("Failed to load article");
        }
      } catch (err) {
        console.error("Error fetching blog post:", err);
        setError("Failed to load article");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPost();
  }, [slug, market, language]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatReadTime = (minutes?: number) => {
    if (!minutes) return "5 min read";
    return `${minutes} min read`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <Navbar />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading article...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {error === "Article not found"
              ? "Article Not Found"
              : "Error Loading Article"}
          </h1>
          <p className="text-gray-600 mb-8">
            {error === "Article not found"
              ? "The article you're looking for doesn't exist."
              : "There was an error loading the article. Please try again."}
          </p>
          <div className="space-x-4">
            <Link href="/blog">
              <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
                Back to Blog
              </Button>
            </Link>
            {error !== "Article not found" && (
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <SEOHead post={post} market={market} language={language} />
      <Navbar />
      {/* Header */}
      <div className="pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Link href="/blog">
              <Button
                variant="ghost"
                className="mb-8 p-0 h-auto text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Button>
            </Link>

            <div className="flex items-center gap-4 mb-6">
              <Badge
                variant="outline"
                className="text-blue-600 border-blue-200"
              >
                {post.blog_categories?.name || "Uncategorized"}
              </Badge>
              <div className="flex items-center text-sm text-gray-500 gap-4">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {formatDate(post.published_at)}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {formatReadTime(post.read_time_minutes)}
                </div>
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {post.title}
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {post.summary}
            </p>

            <div className="text-sm text-gray-500 mb-12">
              By {post.website_admins.name}
            </div>

            {/* Featured Image */}
            {post.featured_image_url && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="mb-12"
              >
                <div className="aspect-video rounded-2xl overflow-hidden bg-gray-100 shadow-lg">
                  <img
                    src={post.featured_image_url}
                    alt={post.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] p-8 md:p-12 border border-white/20"
          >
            <div
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(post.content, {
                  ALLOWED_TAGS: [
                    "p",
                    "br",
                    "strong",
                    "em",
                    "u",
                    "h1",
                    "h2",
                    "h3",
                    "h4",
                    "h5",
                    "h6",
                    "ul",
                    "ol",
                    "li",
                    "blockquote",
                    "a",
                    "img",
                    "div",
                    "span",
                    "table",
                    "thead",
                    "tbody",
                    "tr",
                    "th",
                    "td",
                    "pre",
                    "code",
                  ],
                  ALLOWED_ATTR: [
                    "href",
                    "src",
                    "alt",
                    "title",
                    "class",
                    "id",
                    "target",
                    "rel",
                  ],
                  ALLOW_DATA_ATTR: false,
                }),
              }}
            />
          </motion.div>
        </div>
      </div>
    </div>
  );
}

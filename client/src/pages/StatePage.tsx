import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import SocialProof from "@/components/SocialProof";
import ProblemSolution from "@/components/ProblemSolution";
import FeatureTrio from "@/components/FeatureTrio";
import RoiCalculator from "@/components/RoiCalculator";
import Testimonials from "@/components/Testimonials";
import Pricing from "@/components/Pricing";
import SecurityBanner from "@/components/SecurityBanner";
import Footer from "@/components/Footer";
import CookieNotice from "@/components/CookieNotice";

type StatePageProps = {
  state: "TX" | "FL" | "NY";
};

// This data would normally come from an API or CMS
const stateData = {
  TX: {
    heroTitle:
      "Your AI Legal Associate for Texas Law—Works While You're in Court.",
    heroSubtitle:
      "Texas-specific research, drafting, and organization tools designed for solo and small firms.",
  },
  FL: {
    heroTitle: "Florida Law Meets AI—Your Legal Associate That Never Sleeps.",
    heroSubtitle:
      "Florida-specific legal research, automated drafting, and practice management for solo attorneys.",
  },
  NY: {
    heroTitle: "New York Legal Practice, Amplified by AI.",
    heroSubtitle:
      "NY-specific legal tools for research, drafting, and client intake—all at solo-firm prices.",
  },
};

export default function StatePage({ state }: StatePageProps) {
  // Set page title based on state
  useEffect(() => {
    document.title = `AiLex - AI Legal Associate for ${state} Lawyers`;
  }, [state]);

  const stateContent = stateData[state];

  return (
    <div className="relative overflow-x-hidden">
      <Navbar currentState={state} />
      <main>
        <Hero
          customTitle={stateContent.heroTitle}
          customSubtitle={stateContent.heroSubtitle}
          state={state}
        />
        <SocialProof />
        <ProblemSolution />
        <FeatureTrio state={state} />
        <RoiCalculator />
        <Testimonials defaultState={state} />
        <Pricing />
        <SecurityBanner />
      </main>
      <Footer />
      <CookieNotice />

      {/* Mobile action button */}
      <div className="fixed md:hidden bottom-6 right-6 z-40">
        <button className="bg-primary text-white h-14 w-14 rounded-full shadow-lg flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        </button>
      </div>

      {/* State selector */}
      <div className="fixed top-20 right-4 z-30 md:block hidden">
        <div className="bg-white rounded-lg shadow-md p-2">
          <select
            value={state}
            onChange={(e) => {
              window.location.href = `/${e.target.value.toLowerCase()}`;
            }}
            className="text-sm font-medium outline-none"
          >
            <option value="TX">Texas</option>
            <option value="FL">Florida</option>
            <option value="NY">New York</option>
          </select>
        </div>
      </div>
    </div>
  );
}

import { createClient } from "@supabase/supabase-js";

const supabaseUrl =
  import.meta.env.VITE_SUPABASE_URL ||
  "https://anwefmklplkjxkmzpnva.supabase.co";
const supabaseAnonKey =
  import.meta.env.VITE_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc3OTYyNDYsImV4cCI6MjA1MzM3MjI0Nn0._mrkLcgDRn-ejKwHGEC49L2C4SVvNdBcLfJbaFwpTkU";

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Admin authentication functions
export interface AdminUser {
  id: string;
  email: string;
  password_hash: string;
  name: string;
  is_active: string;
  last_login_at?: string;
  password_reset_token?: string;
  password_reset_expires?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminLoginCredentials {
  email: string;
  password: string;
}

export interface AdminAuthResponse {
  success: boolean;
  user?: AdminUser;
  token?: string;
  error?: string;
}

export async function adminLogin(
  credentials: AdminLoginCredentials
): Promise<AdminAuthResponse> {
  try {
    const response = await fetch("/api/websiteadmin/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(credentials),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.error || "Login failed",
      };
    }

    return data;
  } catch (error) {
    console.error("Admin login error:", error);
    return {
      success: false,
      error: "Login failed. Please try again.",
    };
  }
}

export async function verifyAdminToken(
  token: string
): Promise<AdminUser | null> {
  try {
    const response = await fetch("/api/websiteadmin/verify-token", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token }),
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();

    if (!data.success) {
      return null;
    }

    return data.user;
  } catch (error) {
    console.error("Token verification error:", error);
    return null;
  }
}

export async function createAdminUser(admin: {
  email: string;
  password: string;
  name: string;
  role?: string;
}): Promise<AdminUser | null> {
  try {
    const { data, error } = await supabase
      .from("website_admins")
      .insert([
        {
          email: admin.email,
          password: admin.password, // In production, hash this password
          name: admin.name,
          role: admin.role || "admin",
          is_active: true,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Create admin error:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Create admin error:", error);
    return null;
  }
}

export async function getAllAdminUsers(): Promise<AdminUser[]> {
  try {
    const { data, error } = await supabase
      .from("website_admins")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Get admin users error:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Get admin users error:", error);
    return [];
  }
}

export async function updateAdminUser(
  id: number,
  updates: Partial<AdminUser>
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("website_admins")
      .update(updates)
      .eq("id", id);

    return !error;
  } catch (error) {
    console.error("Update admin user error:", error);
    return false;
  }
}

export async function deactivateAdminUser(id: number): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("website_admins")
      .update({ is_active: false })
      .eq("id", id);

    return !error;
  } catch (error) {
    console.error("Deactivate admin user error:", error);
    return false;
  }
}

// Password Reset Functions
export interface PasswordResetResponse {
  success: boolean;
  message: string;
  error?: string;
}

export async function requestPasswordReset(
  email: string
): Promise<PasswordResetResponse> {
  try {
    const response = await fetch("/api/websiteadmin/forgot-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "Failed to send reset email",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "Password reset email sent successfully",
    };
  } catch (error) {
    console.error("Password reset request error:", error);
    return {
      success: false,
      message: "Failed to send reset email. Please try again.",
      error: "Network error",
    };
  }
}

export async function validateResetToken(
  token: string
): Promise<PasswordResetResponse> {
  try {
    const response = await fetch(
      `/api/websiteadmin/validate-reset-token/${token}`
    );
    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "Invalid or expired reset token",
        error: data.error,
      };
    }

    return {
      success: true,
      message: "Token is valid",
    };
  } catch (error) {
    console.error("Token validation error:", error);
    return {
      success: false,
      message: "Failed to validate token. Please try again.",
      error: "Network error",
    };
  }
}

export async function resetPassword(
  token: string,
  newPassword: string
): Promise<PasswordResetResponse> {
  try {
    const response = await fetch("/api/websiteadmin/reset-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token, newPassword }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "Failed to reset password",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "Password reset successfully",
    };
  } catch (error) {
    console.error("Password reset error:", error);
    return {
      success: false,
      message: "Failed to reset password. Please try again.",
      error: "Network error",
    };
  }
}

{"chunks": {"assets/index-Bj71r7dH.js": {"id": "assets/index-Bj71r7dH.js", "isEntry": true, "size": 166003, "sizeGzip": 47618, "sizeBrotli": 41376, "contents": {"../../node_modules/react-dom/client.js": {"filePath": "../../node_modules/react-dom/client.js", "size": 1046, "packageName": "react-dom"}, "../../client/src/lib/queryClient.ts": {"filePath": "../../client/src/lib/queryClient.ts", "size": 390}, "../../client/src/hooks/use-toast.ts": {"filePath": "../../client/src/hooks/use-toast.ts", "size": 1187}, "../../node_modules/clsx/dist/clsx.mjs": {"filePath": "../../node_modules/clsx/dist/clsx.mjs", "size": 362, "packageName": "clsx"}, "../../node_modules/class-variance-authority/dist/index.mjs": {"filePath": "../../node_modules/class-variance-authority/dist/index.mjs", "size": 749, "packageName": "class-variance-authority"}, "../../node_modules/tailwind-merge/dist/bundle-mjs.mjs": {"filePath": "../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "size": 19964, "packageName": "tailwind-merge"}, "../../client/src/lib/utils.ts": {"filePath": "../../client/src/lib/utils.ts", "size": 803}, "../../client/src/components/ui/toast.tsx": {"filePath": "../../client/src/components/ui/toast.tsx", "size": 2635}, "../../client/src/components/ui/toaster.tsx": {"filePath": "../../client/src/components/ui/toaster.tsx", "size": 295}, "../../client/src/components/ui/tooltip.tsx": {"filePath": "../../client/src/components/ui/tooltip.tsx", "size": 589}, "../../node_modules/@supabase/functions-js/dist/module/helper.js": {"filePath": "../../node_modules/@supabase/functions-js/dist/module/helper.js", "size": 216, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/functions-js/dist/module/types.js": {"filePath": "../../node_modules/@supabase/functions-js/dist/module/types.js", "size": 841, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/functions-js/dist/module/FunctionsClient.js": {"filePath": "../../node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "size": 1806, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/node-fetch/browser.js": {"filePath": "../../node_modules/@supabase/node-fetch/browser.js", "size": 424, "packageName": "@supabase/browser.js"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "size": 209, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "size": 2685, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "size": 2034, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "size": 2792, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "size": 2313, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/version.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/version.js", "size": 97, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/constants.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "size": 154, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "size": 1051, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/cjs/index.js": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/cjs/index.js", "size": 763, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs": {"filePath": "../../node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "size": 147, "packageName": "@supabase/dist"}, "../../node_modules/isows/_esm/utils.js": {"filePath": "../../node_modules/isows/_esm/utils.js", "size": 284, "packageName": "isows"}, "../../node_modules/isows/_esm/native.js": {"filePath": "../../node_modules/isows/_esm/native.js", "size": 14, "packageName": "isows"}, "../../node_modules/@supabase/realtime-js/dist/module/lib/version.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/lib/version.js", "size": 13, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/lib/constants.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "size": 641, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/lib/serializer.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "size": 515, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/lib/timer.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "size": 311, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/lib/transformers.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "size": 1917, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/lib/push.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/lib/push.js", "size": 1581, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "size": 2527, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "size": 8366, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js": {"filePath": "../../node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "size": 7153, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/lib/errors.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/lib/errors.js", "size": 433, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/lib/helpers.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "size": 920, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/lib/fetch.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "size": 1582, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "size": 7117, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/lib/version.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/lib/version.js", "size": 17, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/lib/constants.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/lib/constants.js", "size": 40, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "size": 1886, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/storage-js/dist/module/StorageClient.js": {"filePath": "../../node_modules/@supabase/storage-js/dist/module/StorageClient.js", "size": 114, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/lib/version.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/lib/version.js", "size": 18, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/lib/constants.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "size": 330, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/lib/fetch.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "size": 755, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/lib/helpers.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "size": 930, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/version.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/version.js", "size": 18, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/constants.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/constants.js", "size": 299, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/errors.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/errors.js", "size": 1745, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/base64url.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "size": 2301, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/helpers.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "size": 4836, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/fetch.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "size": 3395, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/types.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/types.js", "size": 37, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "size": 3665, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/local-storage.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "size": 100, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/polyfills.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "size": 259, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/lib/locks.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/lib/locks.js", "size": 1579, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/GoTrueClient.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "size": 41598, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/auth-js/dist/module/AuthClient.js": {"filePath": "../../node_modules/@supabase/auth-js/dist/module/AuthClient.js", "size": 12, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "size": 45, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "size": 3487, "packageName": "@supabase/dist"}, "../../node_modules/@supabase/supabase-js/dist/module/index.js": {"filePath": "../../node_modules/@supabase/supabase-js/dist/module/index.js", "size": 485, "packageName": "@supabase/dist"}, "../../client/src/lib/supabase.ts": {"filePath": "../../client/src/lib/supabase.ts", "size": 2468}, "../../client/src/services/translationService.ts": {"filePath": "../../client/src/services/translationService.ts", "size": 4350}, "../../client/src/contexts/TranslationContext.tsx": {"filePath": "../../client/src/contexts/TranslationContext.tsx", "size": 2379}, "../../client/src/components/ui/spinner.tsx": {"filePath": "../../client/src/components/ui/spinner.tsx", "size": 562}, "../../client/src/hooks/useGeoLocation.ts": {"filePath": "../../client/src/hooks/useGeoLocation.ts", "size": 2280}, "../../client/src/hooks/useAutoRedirect.ts": {"filePath": "../../client/src/hooks/useAutoRedirect.ts", "size": 1694}, "../../client/src/components/AutoRedirectHandler.tsx": {"filePath": "../../client/src/components/AutoRedirectHandler.tsx", "size": 3221}, "../../client/src/App.tsx": {"filePath": "../../client/src/App.tsx", "size": 2942}, "../../client/src/main.tsx": {"filePath": "../../client/src/main.tsx", "size": 176}}}}}
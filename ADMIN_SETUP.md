# Admin Portal Setup

This document explains how to set up and use the admin portal for the AiLex website.

## Overview

The admin portal provides secure access to administrative functions through a dedicated login system. It uses Supabase for authentication and data storage.

## URLs

- **Admin Login**: `/admin-secret-portal`
- **Admin Dashboard**: `/admin-secret-portal/dashboard`

## Setup Instructions

### 1. Database Setup

Run the SQL script to create the admin table in your Supabase database:

```sql
-- Execute the contents of scripts/setup-admin-table.sql in your Supabase SQL editor
```

### 2. Environment Configuration

Create a `.env` file in the project root with your Supabase credentials:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 3. Create Admin Users

You can create admin users in several ways:

#### Option A: Using the SQL script (recommended for first admin)
The setup script creates a default admin user:
- Email: `<EMAIL>`
- Password: `admin123`

**⚠️ IMPORTANT: Change this password immediately in production!**

#### Option B: Using Supabase dashboard
1. Go to your Supabase project dashboard
2. Navigate to Table Editor > website_admin
3. Insert a new row with the required fields

#### Option C: Using the createAdminUser function
```typescript
import { createAdminUser } from '@/lib/supabase';

await createAdminUser({
  email: '<EMAIL>',
  password: 'secure-password', // Hash this in production!
  name: 'Admin Name',
  role: 'admin'
});
```

## Security Considerations

### Password Security
- **Current Implementation**: Passwords are stored in plain text (NOT SECURE)
- **Production Requirement**: Implement proper password hashing using bcrypt or similar
- **Recommendation**: Use Supabase Auth instead of custom authentication

### Token Security
- **Current Implementation**: Simple base64 encoding (NOT SECURE)
- **Production Requirement**: Use proper JWT tokens with expiration
- **Recommendation**: Implement refresh tokens for better security

### Database Security
- Row Level Security (RLS) is enabled on the admin table
- Basic policies are in place but should be reviewed for your specific needs
- Consider implementing additional security measures like IP restrictions

## Features

### Admin Login
- Email/password authentication
- Token-based session management
- Automatic redirect to dashboard on successful login

### Admin Dashboard
- User statistics and metrics
- Recent activity monitoring
- Quick action buttons for common tasks
- Responsive design for mobile and desktop

### Admin Management
- View all admin users
- Create new admin users
- Update admin user information
- Deactivate admin users

## API Functions

The following functions are available in `client/src/lib/supabase.ts`:

- `adminLogin(credentials)` - Authenticate admin user
- `verifyAdminToken(token)` - Verify admin session token
- `createAdminUser(admin)` - Create new admin user
- `getAllAdminUsers()` - Get list of all admin users
- `updateAdminUser(id, updates)` - Update admin user information
- `deactivateAdminUser(id)` - Deactivate admin user

## Development

### Running the Application
```bash
npm run dev
```

### Testing Admin Access
1. Navigate to `/admin-secret-portal`
2. Use the default credentials (if using the setup script)
3. Access the dashboard at `/admin-secret-portal/dashboard`

### Adding New Admin Features
1. Create new components in `client/src/components/admin/`
2. Add routes in `client/src/App.tsx`
3. Implement API functions in `client/src/lib/supabase.ts`
4. Update the dashboard with new quick actions

## Production Deployment

Before deploying to production:

1. **Change default admin credentials**
2. **Implement proper password hashing**
3. **Use secure JWT tokens**
4. **Review and update RLS policies**
5. **Set up proper environment variables**
6. **Enable HTTPS only**
7. **Consider implementing 2FA**
8. **Set up monitoring and logging**

## Troubleshooting

### Common Issues

1. **"Invalid credentials" error**
   - Check that the admin user exists in the database
   - Verify the password is correct
   - Ensure the user is active (`is_active = true`)

2. **"Failed to fetch" error**
   - Check Supabase URL and API key in environment variables
   - Verify network connectivity to Supabase
   - Check browser console for detailed error messages

3. **Redirect loop on login**
   - Clear browser localStorage
   - Check that the token verification is working
   - Verify the admin user exists and is active

### Debug Mode
Enable debug logging by adding this to your browser console:
```javascript
localStorage.setItem('debug', 'admin:*');
```

## Support

For issues or questions about the admin portal, please check:
1. Browser console for error messages
2. Supabase logs in the dashboard
3. Network tab for failed API requests

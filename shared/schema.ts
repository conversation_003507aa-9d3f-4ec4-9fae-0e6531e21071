import {
  pgTable,
  text,
  serial,
  integer,
  boolean,
  timestamp,
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User model for basic authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

// Contact submissions model for newsletter signups and contact forms
export const contactSubmissions = pgTable("contact_submissions", {
  id: serial("id").primaryKey(),
  email: text("email").notNull(),
  name: text("name"),
  message: text("message"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Website admin model for admin authentication
export const websiteAdmins = pgTable("website_admins", {
  id: serial("id").primary<PERSON>ey(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  name: text("name").notNull(),
  role: text("role").notNull().default("admin"),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  lastLoginAt: timestamp("last_login_at"),
});

// Blog categories model for organizing articles
export const blogCategories = pgTable("blog_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  slug: text("slug").notNull().unique(),
  description: text("description"),
  color: text("color").notNull().default("#3B82F6"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Blog markets model for multi-market support
export const blogMarkets = pgTable("blog_markets", {
  id: serial("id").primaryKey(),
  marketCode: text("market_code").notNull().unique(),
  marketName: text("market_name").notNull(),
  languageCode: text("language_code").notNull(),
  languageName: text("language_name").notNull(),
  isDefaultForMarket: boolean("is_default_for_market").default(false),
  isActive: boolean("is_active").default(true),
  urlPath: text("url_path").notNull().unique(),
  displayOrder: integer("display_order").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Blog articles model for content management with multi-market support
export const blogArticles = pgTable("blog_articles", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  summary: text("summary"),
  content: text("content").notNull(),
  categoryId: integer("category_id").references(() => blogCategories.id),
  authorId: text("author_id").notNull(), // UUID reference to website_admins
  status: text("status").notNull().default("draft"), // draft, published, archived
  featuredImageUrl: text("featured_image_url"),
  metaTitle: text("meta_title"),
  metaDescription: text("meta_description"),
  readTimeMinutes: integer("read_time_minutes"),
  publishedAt: timestamp("published_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  // Multi-market fields
  market: text("market").notNull().default("global"), // global, usa, be-fr, be-nl
  language: text("language").notNull().default("en"), // en, fr, nl
  translationKeyId: text("translation_key_id"), // UUID reference to translation_keys
  isTranslation: boolean("is_translation").default(false),
  baseArticleId: integer("base_article_id"), // Self-reference, will be constrained in DB
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertContactSubmissionSchema = createInsertSchema(
  contactSubmissions
).pick({
  email: true,
  name: true,
  message: true,
});

export const insertWebsiteAdminSchema = createInsertSchema(websiteAdmins).pick({
  email: true,
  password: true,
  name: true,
  role: true,
});

export const adminLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

export const insertBlogCategorySchema = createInsertSchema(blogCategories).pick(
  {
    name: true,
    slug: true,
    description: true,
    color: true,
  }
);

export const insertBlogMarketSchema = createInsertSchema(blogMarkets).pick({
  marketCode: true,
  marketName: true,
  languageCode: true,
  languageName: true,
  isDefaultForMarket: true,
  isActive: true,
  urlPath: true,
  displayOrder: true,
});

export const insertBlogArticleSchema = createInsertSchema(blogArticles)
  .pick({
    title: true,
    summary: true,
    content: true,
    categoryId: true,
    status: true,
    featuredImageUrl: true,
    metaTitle: true,
    metaDescription: true,
    readTimeMinutes: true,
    publishedAt: true,
    market: true,
    language: true,
    translationKeyId: true,
    isTranslation: true,
    baseArticleId: true,
  })
  .extend({
    slug: z.string().optional(), // Make slug completely optional - server will handle generation
  });

export const updateBlogArticleSchema = createInsertSchema(blogArticles)
  .pick({
    title: true,
    slug: true,
    summary: true,
    content: true,
    categoryId: true,
    status: true,
    featuredImageUrl: true,
    metaTitle: true,
    metaDescription: true,
    readTimeMinutes: true,
    publishedAt: true,
    market: true,
    language: true,
    translationKeyId: true,
    isTranslation: true,
    baseArticleId: true,
  })
  .partial();

// Blog article status validation
export const blogArticleStatusSchema = z.enum([
  "draft",
  "published",
  "archived",
]);

// Market and language validation
export const blogMarketSchema = z.enum(["global", "usa", "be-fr", "be-nl"]);
export const blogLanguageSchema = z.enum(["en", "fr", "nl"]);

// Blog article query schemas
export const blogArticleQuerySchema = z.object({
  status: blogArticleStatusSchema.optional(),
  categoryId: z.coerce.number().optional(),
  market: blogMarketSchema.optional(),
  language: blogLanguageSchema.optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertContactSubmission = z.infer<
  typeof insertContactSubmissionSchema
>;
export type ContactSubmission = typeof contactSubmissions.$inferSelect;

export type InsertWebsiteAdmin = z.infer<typeof insertWebsiteAdminSchema>;
export type WebsiteAdmin = typeof websiteAdmins.$inferSelect;
export type AdminLogin = z.infer<typeof adminLoginSchema>;

// Blog types
export type InsertBlogCategory = z.infer<typeof insertBlogCategorySchema>;
export type BlogCategory = typeof blogCategories.$inferSelect;

export type InsertBlogMarket = z.infer<typeof insertBlogMarketSchema>;
export type BlogMarket = typeof blogMarkets.$inferSelect;

export type InsertBlogArticle = z.infer<typeof insertBlogArticleSchema>;
export type UpdateBlogArticle = z.infer<typeof updateBlogArticleSchema>;
export type BlogArticle = typeof blogArticles.$inferSelect;
export type BlogArticleStatus = z.infer<typeof blogArticleStatusSchema>;
export type BlogMarketType = z.infer<typeof blogMarketSchema>;
export type BlogLanguageType = z.infer<typeof blogLanguageSchema>;
export type BlogArticleQuery = z.infer<typeof blogArticleQuerySchema>;

// Extended blog article type with category and author information
export type BlogArticleWithDetails = BlogArticle & {
  category?: BlogCategory;
  author: Pick<WebsiteAdmin, "id" | "name" | "email">;
};

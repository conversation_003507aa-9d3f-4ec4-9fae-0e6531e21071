import "dotenv/config";
import express from "express";
import { registerRoutes } from "../server/routes.js";
import { serveStatic } from "../server/vite.js";

// Create and configure the Express app
let app = null;

async function createApp() {
  if (app) return app;

  console.log("Creating Express app...");
  app = express();
  app.use(express.json());
  app.use(express.urlencoded({ extended: false }));

  // Add logging middleware
  app.use((req, res, next) => {
    const start = Date.now();
    const path = req.path;
    let capturedJsonResponse;

    const originalResJson = res.json;
    res.json = function (bodyJson, ...args) {
      capturedJsonResponse = bodyJson;
      return originalResJson.apply(res, [bodyJson, ...args]);
    };

    res.on("finish", () => {
      const duration = Date.now() - start;
      // Log all requests, not just API requests, for debugging
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }
      console.log(logLine);
    });

    next();
  });

  try {
    console.log("Registering routes...");
    // Initialize routes
    await registerRoutes(app);

    console.log("Setting up error handling...");
    // Error handling middleware
    app.use((err, _req, res, _next) => {
      const status = err.status || err.statusCode || 500;
      const message = err.message || "Internal Server Error";
      res.status(status).json({ message });
      console.error(err);
    });

    console.log("Setting up static file serving...");
    // Setup static file serving for production
    serveStatic(app);

    console.log("App initialization complete");
  } catch (error) {
    console.error("Failed to initialize app:", error);
    throw error;
  }

  return app;
}

// Export the handler function for Vercel
export default async function handler(req, res) {
  try {
    console.log("Handler called:", req.method, req.url);
    const app = await createApp();
    return app(req, res);
  } catch (error) {
    console.error("Handler error:", error);
    res.status(500).json({ error: "Internal Server Error", message: error.message, stack: error.stack });
  }
}
-- Fix author_id type mismatch in blog_articles table
-- The website_admins.id is actually UUID (text), but blog_articles.author_id was INTEGER

-- First, check if there are any existing articles (there shouldn't be any yet)
-- If there are, this migration will need to be more careful

-- Drop the foreign key constraint first
ALTER TABLE blog_articles DROP CONSTRAINT IF EXISTS blog_articles_author_id_fkey;

-- Change the column type from INTEGER to TEXT to match UUID
ALTER TABLE blog_articles ALTER COLUMN author_id TYPE TEXT;

-- Add the foreign key constraint back
-- Note: We can't add a proper foreign key constraint because website_admins.id is UUID
-- and PostgreSQL foreign keys need exact type matches
-- The application will handle referential integrity

-- Add a comment to document the relationship
COMMENT ON COLUMN blog_articles.author_id IS 'UUID reference to website_admins.id';

-- Update the index if it exists
DROP INDEX IF EXISTS idx_blog_articles_author_id;
CREATE INDEX IF NOT EXISTS idx_blog_articles_author_id ON blog_articles(author_id);

-- Create the website_admins table
CREATE TABLE IF NOT EXISTS website_admins (
    id SERIAL PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'admin',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Create an index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_website_admins_email ON website_admins(email);

-- Create an index on is_active for faster filtering
CREATE INDEX IF NOT EXISTS idx_website_admins_active ON website_admins(is_active);

-- Insert a default admin user (change the password!)
-- Note: In production, passwords should be properly hashed
INSERT INTO website_admins (email, password, name, role)
VALUES ('<EMAIL>', 'admin123', 'System Administrator', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Enable Row Level Security (RLS) for security
ALTER TABLE website_admins ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows admins to read their own data
-- Note: This is a basic policy - adjust based on your security requirements
CREATE POLICY "Admin users can view their own data" ON website_admins
    FOR SELECT USING (auth.uid()::text = id::text OR auth.role() = 'service_role');

-- Create a policy for updates
CREATE POLICY "Admin users can update their own data" ON website_admins
    FOR UPDATE USING (auth.uid()::text = id::text OR auth.role() = 'service_role');

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON website_admins TO anon;
GRANT SELECT, INSERT, UPDATE ON website_admins TO authenticated;

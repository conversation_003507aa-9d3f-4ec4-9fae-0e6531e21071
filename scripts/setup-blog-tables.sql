-- Blog CMS Database Setup
-- This script creates the necessary tables for the blog content management system

-- Create the blog_categories table
CREATE TABLE IF NOT EXISTS blog_categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT NOT NULL DEFAULT '#3B82F6',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the blog_articles table
CREATE TABLE IF NOT EXISTS blog_articles (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    summary TEXT,
    content TEXT NOT NULL,
    category_id INTEGER REFERENCES blog_categories(id),
    author_id INTEGER REFERENCES website_admins(id) NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    featured_image_url TEXT,
    meta_title TEXT,
    meta_description TEXT,
    read_time_minutes INTEGER,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON blog_categories(slug);
CREATE INDEX IF NOT EXISTS idx_blog_categories_name ON blog_categories(name);

CREATE INDEX IF NOT EXISTS idx_blog_articles_slug ON blog_articles(slug);
CREATE INDEX IF NOT EXISTS idx_blog_articles_status ON blog_articles(status);
CREATE INDEX IF NOT EXISTS idx_blog_articles_category_id ON blog_articles(category_id);
CREATE INDEX IF NOT EXISTS idx_blog_articles_author_id ON blog_articles(author_id);
CREATE INDEX IF NOT EXISTS idx_blog_articles_published_at ON blog_articles(published_at);
CREATE INDEX IF NOT EXISTS idx_blog_articles_created_at ON blog_articles(created_at);

-- Create a composite index for published articles by category
CREATE INDEX IF NOT EXISTS idx_blog_articles_published_category ON blog_articles(status, category_id, published_at DESC) 
WHERE status = 'published';

-- Create a full-text search index for title and content
CREATE INDEX IF NOT EXISTS idx_blog_articles_search ON blog_articles USING gin(to_tsvector('english', title || ' ' || content));

-- Enable Row Level Security (RLS) for security
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_articles ENABLE ROW LEVEL SECURITY;

-- Create policies for blog_categories
-- Allow public read access to categories
CREATE POLICY "Public can view blog categories" ON blog_categories
    FOR SELECT USING (true);

-- Allow admins to manage categories
CREATE POLICY "Admins can manage blog categories" ON blog_categories
    FOR ALL USING (auth.role() = 'service_role');

-- Create policies for blog_articles
-- Allow public read access to published articles
CREATE POLICY "Public can view published articles" ON blog_articles
    FOR SELECT USING (status = 'published');

-- Allow admins to manage all articles
CREATE POLICY "Admins can manage all articles" ON blog_articles
    FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT SELECT ON blog_categories TO anon;
GRANT SELECT ON blog_categories TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON blog_categories TO service_role;

GRANT SELECT ON blog_articles TO anon;
GRANT SELECT ON blog_articles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON blog_articles TO service_role;

-- Grant sequence permissions
GRANT USAGE, SELECT ON SEQUENCE blog_categories_id_seq TO service_role;
GRANT USAGE, SELECT ON SEQUENCE blog_articles_id_seq TO service_role;

-- Insert default blog categories
INSERT INTO blog_categories (name, slug, description, color) VALUES
    ('Research Tips', 'research-tips', 'Legal research techniques and best practices', '#3B82F6'),
    ('Practice Management', 'practice-management', 'Tips for managing a successful legal practice', '#10B981'),
    ('Business Growth', 'business-growth', 'Strategies for growing your legal business', '#8B5CF6'),
    ('Technology', 'technology', 'Legal technology and digital tools', '#F59E0B'),
    ('Industry News', 'industry-news', 'Latest news and updates in the legal industry', '#EF4444')
ON CONFLICT (slug) DO NOTHING;

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_blog_article_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER trigger_update_blog_article_updated_at
    BEFORE UPDATE ON blog_articles
    FOR EACH ROW
    EXECUTE FUNCTION update_blog_article_updated_at();

-- Create a function to calculate reading time based on content
CREATE OR REPLACE FUNCTION calculate_reading_time(content_text TEXT)
RETURNS INTEGER AS $$
DECLARE
    word_count INTEGER;
    reading_time INTEGER;
BEGIN
    -- Count words (approximate)
    word_count := array_length(string_to_array(regexp_replace(content_text, '<[^>]*>', '', 'g'), ' '), 1);
    
    -- Calculate reading time (assuming 200 words per minute)
    reading_time := GREATEST(1, ROUND(word_count / 200.0));
    
    RETURN reading_time;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically calculate reading time
CREATE OR REPLACE FUNCTION update_blog_article_reading_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.read_time_minutes = calculate_reading_time(NEW.content);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_calculate_reading_time
    BEFORE INSERT OR UPDATE OF content ON blog_articles
    FOR EACH ROW
    EXECUTE FUNCTION update_blog_article_reading_time();

import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { type Server } from "http";
import { nanoid } from "nanoid";

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  // Vite setup only needed in development
  if (process.env.NODE_ENV !== "development") {
    return;
  }

  try {
    const { createServer: createViteServer, createLogger } = await import(
      "vite"
    );
    const viteConfig = (await import("../vite.config.js")).default;
    const viteLogger = createLogger();

    const serverOptions = {
      middlewareMode: true as const,
      hmr: { server },
      allowedHosts: true as const,
    };

    const vite = await createViteServer({
      ...viteConfig,
      configFile: false,
      customLogger: {
        ...viteLogger,
        error: (msg, options) => {
          viteLogger.error(msg, options);
          process.exit(1);
        },
      },
      server: serverOptions,
      appType: "custom",
    });

    (app as any).use(vite.middlewares);
    (app as any).use("*", async (req: any, res: any, next: any) => {
      const url = req.originalUrl;

      try {
        const clientTemplate = path.resolve(
          import.meta.dirname,
          "..",
          "client",
          "index.html"
        );

        // always reload the index.html file from disk incase it changes
        let template = await fs.promises.readFile(clientTemplate, "utf-8");
        template = template.replace(
          `src="/src/main.tsx"`,
          `src="/src/main.tsx?v=${nanoid()}"`
        );
        const page = await vite.transformIndexHtml(url, template);
        (res as any).status(200).set({ "Content-Type": "text/html" }).end(page);
      } catch (e) {
        vite.ssrFixStacktrace(e as Error);
        next(e);
      }
    });
  } catch (error) {
    console.warn("Failed to setup Vite in development:", error);
  }
}

export function serveStatic(app: Express) {
  // In production (Vercel), the files are in dist/public
  // In development, they would be in server/public
  let distPath = path.resolve(import.meta.dirname, "public");

  // Check if we're in production and the dist/public directory exists
  const prodPath = path.resolve(import.meta.dirname, "..", "dist", "public");
  if (process.env.NODE_ENV === "production" && fs.existsSync(prodPath)) {
    distPath = prodPath;
    console.log("Using production static files path:", distPath);
  }

  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }

  (app as any).use(express.static(distPath));

  // fall through to index.html if the file doesn't exist
  (app as any).use("*", (req: any, res: any) => {
    console.log(`Serving index.html for path: ${req.originalUrl}`);
    (res as any).sendFile(path.resolve(distPath, "index.html"));
  });
}

import { Resend } from "resend";

// Initialize Resend client
const resendApiKey =
  process.env.RESEND_API_KEY || "re_BRaTQns1_HH3gkuPAWrcjSSgAbSuDRotB";
const resend = new Resend(resendApiKey);

// Email configuration
const FROM_EMAIL = process.env.FROM_EMAIL || "AiLex Admin <<EMAIL>>";
const ADMIN_PORTAL_URL =
  process.env.ADMIN_PORTAL_URL ||
  (process.env.NODE_ENV === "production"
    ? "https://ailexlaw.com/admin-secret-portal"
    : "http://localhost:5000/admin-secret-portal");

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    const { data, error } = await resend.emails.send({
      from: FROM_EMAIL,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    });

    if (error) {
      console.error("Email sending error:", error);
      return false;
    }

    console.log("Email sent successfully:", data?.id);
    return true;
  } catch (error) {
    console.error("Email service error:", error);
    return false;
  }
}

export function generatePasswordResetEmail(
  adminName: string,
  adminEmail: string,
  resetToken: string
): EmailOptions {
  const resetUrl = `${ADMIN_PORTAL_URL}/reset-password/${resetToken}`;

  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Password Reset - AiLex Admin Portal</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .container {
          background-color: white;
          border-radius: 8px;
          padding: 40px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #1e40af;
          margin-bottom: 10px;
        }
        .title {
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 20px;
        }
        .content {
          margin-bottom: 30px;
        }
        .button {
          display: inline-block;
          background-color: #1e40af;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 500;
          margin: 20px 0;
        }
        .button:hover {
          background-color: #1d4ed8;
        }
        .footer {
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          font-size: 14px;
          color: #6b7280;
        }
        .warning {
          background-color: #fef3c7;
          border: 1px solid #f59e0b;
          border-radius: 6px;
          padding: 15px;
          margin: 20px 0;
        }
        .warning-title {
          font-weight: 600;
          color: #92400e;
          margin-bottom: 5px;
        }
        .warning-text {
          color: #92400e;
          font-size: 14px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">AiLex</div>
          <div style="color: #6b7280;">Admin Portal</div>
        </div>
        
        <h1 class="title">Password Reset Request</h1>
        
        <div class="content">
          <p>Hello ${adminName},</p>
          
          <p>We received a request to reset your password for the AiLex Admin Portal. If you made this request, click the button below to reset your password:</p>
          
          <div style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Your Password</a>
          </div>
          
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${resetUrl}
          </p>
          
          <div class="warning">
            <div class="warning-title">⚠️ Security Notice</div>
            <div class="warning-text">
              This password reset link will expire in 30 minutes for security reasons. If you didn't request this password reset, please ignore this email or contact support if you have concerns.
            </div>
          </div>
        </div>
        
        <div class="footer">
          <p>This email was sent to ${adminEmail} because a password reset was requested for your AiLex Admin Portal account.</p>
          <p>If you have any questions, please contact our support team.</p>
          <p style="margin-top: 20px;">
            <strong>AiLex Team</strong><br>
            Your AI Legal Assistant
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Password Reset Request - AiLex Admin Portal
    
    Hello ${adminName},
    
    We received a request to reset your password for the AiLex Admin Portal.
    
    To reset your password, please visit the following link:
    ${resetUrl}
    
    This link will expire in 30 minutes for security reasons.
    
    If you didn't request this password reset, please ignore this email.
    
    Best regards,
    The AiLex Team
  `;

  return {
    to: adminEmail,
    subject: "Reset Your AiLex Admin Portal Password",
    html,
    text,
  };
}

export async function sendPasswordResetEmail(
  adminName: string,
  adminEmail: string,
  resetToken: string
): Promise<boolean> {
  const emailOptions = generatePasswordResetEmail(
    adminName,
    adminEmail,
    resetToken
  );
  return await sendEmail(emailOptions);
}

import request from "supertest";
import express from "express";
import { registerRoutes } from "../routes";

describe("GET /api/ping", () => {
  let app: express.Express;

  beforeAll(async () => {
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: false }));

    // Register routes without starting the server
    await registerRoutes(app);
  });

  it("responds with pong", async () => {
    const res = await request(app).get("/api/ping");
    expect(res.status).toBe(200);
    expect(res.text).toBe("pong");
  });
});

import express from "express";
import type { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";
import crypto from "crypto";
import rateLimit from "express-rate-limit";
import { sendPasswordResetEmail } from "./emailService.js";
import { log } from "./vite.js";

// Extended Request interface for website admin authentication
interface WebsiteAdminRequest extends Request {
  adminUser?: {
    id: string;
    email: string;
    name: string;
    is_active?: string;
  };
}

// Initialize Supabase client for website admin operations
const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Website Admin JWT configuration
const getWebsiteAdminJWTSecret = () => {
  const secret = process.env.WEBSITE_ADMIN_JWT_SECRET;
  if (!secret) {
    throw new Error(
      "WEBSITE_ADMIN_JWT_SECRET environment variable is required"
    );
  }
  return secret;
};

const WEBSITE_ADMIN_TOKEN_EXPIRY =
  process.env.WEBSITE_ADMIN_TOKEN_EXPIRY || "24h";

// Rate limiting for website admin authentication
const websiteAdminAuthLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 login attempts per windowMs
  message: {
    message: "Too many login attempts. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for password reset requests
const websiteAdminPasswordResetLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit each IP to 3 password reset requests per windowMs
  message: {
    message: "Too many password reset requests. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Website Admin authentication middleware (separate from main app)
export async function requireWebsiteAdminAuth(
  req: WebsiteAdminRequest,
  res: Response,
  next: NextFunction
): Promise<void | Response> {
  try {
    const authHeader = (req as any).headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return (res as any)
        .status(401)
        .json({ message: "Authentication required" });
    }

    const token = authHeader.substring(7);

    // Verify JWT token
    const decoded = jwt.verify(
      token,
      getWebsiteAdminJWTSecret()
    ) as jwt.JwtPayload & {
      userId: string;
      email: string;
    };

    // Verify user still exists and is active
    const { data: adminUser, error } = await supabase
      .from("website_admins")
      .select("id, email, name, is_active")
      .eq("id", decoded.userId)
      .eq("is_active", "true")
      .single();

    if (error || !adminUser) {
      return (res as any)
        .status(401)
        .json({ message: "Invalid authentication" });
    }

    req.adminUser = adminUser;
    (next as any)();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return (res as any).status(401).json({ message: "Invalid token" });
    }
    if (error instanceof jwt.TokenExpiredError) {
      return (res as any).status(401).json({ message: "Token expired" });
    }
    log(`Website admin auth middleware error: ${error}`, "websiteadmin-auth");
    return (res as any).status(401).json({ message: "Authentication failed" });
  }
}

// Validation schemas
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

const passwordResetRequestSchema = z.object({
  email: z.string().email("Invalid email address"),
});

const passwordResetSchema = z.object({
  token: z
    .string()
    .min(64, "Invalid reset token")
    .max(64, "Invalid reset token"),
  newPassword: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password must be less than 128 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),
});

export function registerWebsiteAdminRoutes(app: express.Application): void {
  log("Registering website admin routes...", "websiteadmin-routes");

  // Website Admin Login
  (app as any).post(
    "/api/websiteadmin/login",
    websiteAdminAuthLimiter,
    async (req: any, res: any) => {
      try {
        const { email, password } = loginSchema.parse((req as any).body);

        // Get admin user from database
        const { data: adminUser, error } = await supabase
          .from("website_admins")
          .select("*")
          .eq("email", email)
          .eq("is_active", "true")
          .single();

        if (error || !adminUser) {
          return (res as any).status(401).json({
            success: false,
            error: "Invalid email or password",
          });
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(
          password,
          adminUser.password_hash
        );

        if (!isPasswordValid) {
          return (res as any).status(401).json({
            success: false,
            error: "Invalid email or password",
          });
        }

        // Update last login time
        await supabase
          .from("website_admins")
          .update({
            last_login_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq("id", adminUser.id);

        // Generate JWT token
        const token = jwt.sign(
          {
            userId: adminUser.id,
            email: adminUser.email,
          },
          getWebsiteAdminJWTSecret(),
          { expiresIn: WEBSITE_ADMIN_TOKEN_EXPIRY } as jwt.SignOptions
        );

        // Return success response (matching existing client expectations)
        (res as any).json({
          success: true,
          user: {
            id: adminUser.id,
            email: adminUser.email,
            name: adminUser.name,
            is_active: adminUser.is_active,
            last_login_at: adminUser.last_login_at,
            created_at: adminUser.created_at,
            updated_at: adminUser.updated_at,
          },
          token,
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return (res as any).status(400).json({
            success: false,
            error: "Invalid input",
            details: error.format(),
          });
        }

        log(`Website admin login error: ${error}`, "websiteadmin-auth");
        (res as any).status(500).json({
          success: false,
          error: "Login failed. Please try again.",
        });
      }
    }
  );

  // Website Admin Token Verification
  (app as any).post(
    "/api/websiteadmin/verify-token",
    async (req: any, res: any) => {
      try {
        const { token } = (req as any).body;

        if (!token) {
          return (res as any).status(400).json({
            success: false,
            error: "Token is required",
          });
        }

        // Verify JWT token
        const decoded = jwt.verify(
          token,
          getWebsiteAdminJWTSecret()
        ) as jwt.JwtPayload & {
          userId: string;
          email: string;
        };

        // Get current user data
        const { data: adminUser, error } = await supabase
          .from("website_admins")
          .select("*")
          .eq("id", decoded.userId)
          .eq("is_active", "true")
          .single();

        if (error || !adminUser) {
          return (res as any).status(401).json({
            success: false,
            error: "Invalid token",
          });
        }

        (res as any).json({
          success: true,
          user: {
            id: adminUser.id,
            email: adminUser.email,
            name: adminUser.name,
            is_active: adminUser.is_active,
            last_login_at: adminUser.last_login_at,
            created_at: adminUser.created_at,
            updated_at: adminUser.updated_at,
          },
        });
      } catch (error) {
        if (error instanceof jwt.JsonWebTokenError) {
          return (res as any).status(401).json({
            success: false,
            error: "Invalid token",
          });
        }
        if (error instanceof jwt.TokenExpiredError) {
          return (res as any).status(401).json({
            success: false,
            error: "Token expired",
          });
        }

        log(
          `Website admin token verification error: ${error}`,
          "websiteadmin-auth"
        );
        (res as any).status(500).json({
          success: false,
          error: "Token verification failed",
        });
      }
    }
  );

  // Website Admin Password Reset Request
  (app as any).post(
    "/api/websiteadmin/forgot-password",
    websiteAdminPasswordResetLimiter,
    async (req: any, res: any) => {
      try {
        const { email } = passwordResetRequestSchema.parse((req as any).body);

        // Check if admin user exists and is active
        const { data: adminUser, error: userError } = await supabase
          .from("website_admins")
          .select("id, email, name")
          .eq("email", email)
          .eq("is_active", "true")
          .single();

        if (userError || !adminUser) {
          // Don't reveal if user exists or not for security
          return (res as any).status(200).json({
            message:
              "If an account with that email exists, a password reset link has been sent.",
          });
        }

        // Generate secure reset token
        const resetToken = crypto.randomBytes(32).toString("hex");
        const resetExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now

        // Update user with reset token
        const { error: updateError } = await supabase
          .from("website_admins")
          .update({
            password_reset_token: resetToken,
            password_reset_expires: resetExpires.toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq("id", adminUser.id);

        if (updateError) {
          log(
            `Error updating reset token: ${updateError}`,
            "websiteadmin-auth"
          );
          return (res as any).status(500).json({
            message: "Failed to process password reset request.",
          });
        }

        // Send password reset email
        const emailSent = await sendPasswordResetEmail(
          adminUser.name,
          adminUser.email,
          resetToken
        );

        if (!emailSent) {
          log("Failed to send password reset email", "websiteadmin-auth");
          // Don't reveal email sending failure to prevent information disclosure
        }

        // Log token in development for testing
        if (process.env.NODE_ENV === "development") {
          log(
            `Website admin password reset requested for ${email}, token: ${resetToken}`,
            "websiteadmin-auth"
          );
        }

        (res as any).status(200).json({
          message:
            "If an account with that email exists, a password reset link has been sent.",
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return (res as any).status(400).json({
            message: "Invalid email address",
            errors: error.format(),
          });
        }

        log(
          `Error processing website admin password reset request: ${error}`,
          "websiteadmin-auth"
        );
        (res as any).status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Website Admin Password Reset
  (app as any).post(
    "/api/websiteadmin/reset-password",
    async (req: any, res: any) => {
      try {
        const { token, newPassword } = passwordResetSchema.parse(
          (req as any).body
        );

        // Validate token and get user
        const { data: adminUser, error } = await supabase
          .from("website_admins")
          .select("id, email, password_reset_expires")
          .eq("password_reset_token", token)
          .eq("is_active", "true")
          .single();

        if (error || !adminUser) {
          return (res as any).status(400).json({
            message: "Invalid or expired reset token",
          });
        }

        // Check if token has expired
        const now = new Date();
        const expiresAt = new Date(adminUser.password_reset_expires);

        if (now > expiresAt) {
          return (res as any).status(400).json({
            message: "Reset token has expired",
          });
        }

        // Hash the new password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password and clear reset token
        const { error: updateError } = await supabase
          .from("website_admins")
          .update({
            password_hash: hashedPassword,
            password_reset_token: null,
            password_reset_expires: null,
            updated_at: new Date().toISOString(),
          })
          .eq("id", adminUser.id);

        if (updateError) {
          log(
            `Error updating website admin password: ${updateError}`,
            "websiteadmin-auth"
          );
          return (res as any).status(500).json({
            message: "Failed to reset password",
          });
        }

        (res as any).status(200).json({
          message: "Password reset successfully",
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return (res as any).status(400).json({
            message: "Invalid input",
            errors: error.format(),
          });
        }

        log(
          `Error resetting website admin password: ${error}`,
          "websiteadmin-auth"
        );
        (res as any).status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Website Admin Token Validation (for reset tokens)
  (app as any).get(
    "/api/websiteadmin/validate-reset-token/:token",
    async (req: any, res: any) => {
      try {
        const { token } = (req as any).params;

        if (!token) {
          return (res as any).status(400).json({
            message: "Reset token is required",
          });
        }

        // Check if token exists and is not expired
        const { data: adminUser, error } = await supabase
          .from("website_admins")
          .select("id, email, password_reset_expires")
          .eq("password_reset_token", token)
          .eq("is_active", "true")
          .single();

        if (error || !adminUser) {
          return (res as any).status(400).json({
            message: "Invalid or expired reset token",
          });
        }

        // Check if token has expired
        const now = new Date();
        const expiresAt = new Date(adminUser.password_reset_expires);

        if (now > expiresAt) {
          return (res as any).status(400).json({
            message: "Reset token has expired",
          });
        }

        (res as any).status(200).json({
          message: "Reset token is valid",
          email: adminUser.email,
        });
      } catch (error) {
        log(
          `Error validating website admin reset token: ${error}`,
          "websiteadmin-auth"
        );
        (res as any).status(500).json({ message: "Internal server error" });
      }
    }
  );

  log("Website admin routes registered successfully", "websiteadmin-routes");
}
